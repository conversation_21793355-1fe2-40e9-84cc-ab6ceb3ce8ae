import { Page, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';

export class DashboardPage {
  private helpers: TestHelpers;

  // Selectors
  private readonly sidebar = '.sidebar, nav[role="navigation"]';
  private readonly header = '.header, header';
  private readonly userMenu = '.user-menu, [data-testid="user-menu"]';
  private readonly logoutButton = 'button:has-text("Logout"), button:has-text("Keluar")';
  private readonly statsCards = '.stats-card, [data-testid="stats-card"]';
  private readonly appointmentsSection = '.appointments, [data-testid="appointments"]';
  private readonly searchInput = 'input[placeholder*="Cari"], input[type="search"]';
  private readonly notificationBell = '.notification, [data-testid="notification"]';
  private readonly tenantSwitcher = '.tenant-switcher, [data-testid="tenant-switcher"]';

  // Navigation links
  private readonly navLinks = {
    dashboard: 'a[href="/"], a:has-text("Dashboard")',
    patients: 'a[href="/patients"], a:has-text("Pasien")',
    appointments: 'a[href="/appointments"], a:has-text("Jadwal")',
    treatments: 'a[href="/treatments"], a:has-text("Treatment")',
    billing: 'a[href="/billing"], a:has-text("Billing")',
    inventory: 'a[href="/inventory"], a:has-text("Inventory")',
    users: 'a[href="/users"], a:has-text("Users")',
    reports: 'a[href="/reports"], a:has-text("Laporan")',
    settings: 'a[href="/settings"], a:has-text("Pengaturan")'
  };

  constructor(private page: Page) {
    this.helpers = new TestHelpers(page);
  }

  /**
   * Navigate to dashboard
   */
  async goto() {
    await this.page.goto('/');
    await this.helpers.waitForPageLoad();
  }

  /**
   * Verify dashboard is displayed
   */
  async verifyDashboardDisplayed() {
    await expect(this.page.locator(this.sidebar)).toBeVisible();
    await expect(this.page.locator(this.header)).toBeVisible();
    
    // Verify URL is dashboard
    await this.helpers.verifyURL('/');
    
    // Check for dashboard title or heading
    const dashboardIndicators = [
      'h1:has-text("Dashboard")',
      'h1:has-text("Beranda")',
      '.dashboard-title',
      '[data-testid="dashboard-title"]'
    ];
    
    let indicatorFound = false;
    for (const selector of dashboardIndicators) {
      try {
        await expect(this.page.locator(selector)).toBeVisible({ timeout: 2000 });
        indicatorFound = true;
        break;
      } catch {
        // Continue to next indicator
      }
    }
    
    expect(indicatorFound).toBeTruthy();
  }

  /**
   * Verify sidebar navigation
   */
  async verifySidebarNavigation() {
    await expect(this.page.locator(this.sidebar)).toBeVisible();
    
    // Check for logo
    const logo = this.page.locator('.logo, [data-testid="logo"]');
    await expect(logo).toBeVisible();
    
    // Verify main navigation links are present
    for (const [name, selector] of Object.entries(this.navLinks)) {
      await expect(this.page.locator(selector)).toBeVisible();
    }
  }

  /**
   * Navigate to specific module
   */
  async navigateToModule(module: keyof typeof this.navLinks) {
    const selector = this.navLinks[module];
    await this.helpers.clickElement(selector);
    await this.helpers.waitForPageLoad();
  }

  /**
   * Verify stats cards are displayed
   */
  async verifyStatsCards() {
    const statsCards = this.page.locator(this.statsCards);
    await expect(statsCards).toHaveCount(4, { timeout: 10000 });
    
    // Verify each stats card has content
    const count = await statsCards.count();
    for (let i = 0; i < count; i++) {
      const card = statsCards.nth(i);
      await expect(card).toBeVisible();
      
      // Should have a number/value and label
      const hasNumber = await card.locator('.number, .value, .stat-number').count() > 0;
      const hasLabel = await card.locator('.label, .title, .stat-label').count() > 0;
      
      expect(hasNumber || hasLabel).toBeTruthy();
    }
  }

  /**
   * Verify today's appointments section
   */
  async verifyTodayAppointments() {
    const appointmentsSection = this.page.locator(this.appointmentsSection);
    await expect(appointmentsSection).toBeVisible();
    
    // Should have a title
    await expect(appointmentsSection.locator('h2, h3, .title')).toBeVisible();
  }

  /**
   * Use search functionality
   */
  async searchFor(query: string) {
    await this.helpers.fillField(this.searchInput, query);
    await this.page.keyboard.press('Enter');
    await this.helpers.waitForPageLoad();
  }

  /**
   * Open user menu
   */
  async openUserMenu() {
    await this.helpers.clickElement(this.userMenu);
    await expect(this.page.locator('.dropdown, .menu')).toBeVisible();
  }

  /**
   * Logout from application
   */
  async logout() {
    await this.openUserMenu();
    await this.helpers.clickElement(this.logoutButton);
    
    // Wait for redirect to login page
    await this.page.waitForURL(/login|\/$/);
    await this.helpers.waitForPageLoad();
  }

  /**
   * Verify user is logged out
   */
  async verifyLoggedOut() {
    // Should be redirected to login page or see login form
    const loginIndicators = [
      'input[type="email"]',
      'input[name="email"]',
      'h1:has-text("Login")',
      '.login-form'
    ];
    
    let loginFound = false;
    for (const selector of loginIndicators) {
      try {
        await expect(this.page.locator(selector)).toBeVisible({ timeout: 5000 });
        loginFound = true;
        break;
      } catch {
        // Continue to next indicator
      }
    }
    
    expect(loginFound).toBeTruthy();
  }

  /**
   * Check notification bell
   */
  async checkNotifications() {
    await this.helpers.clickElement(this.notificationBell);
    // Add verification for notification dropdown/panel
  }

  /**
   * Switch tenant (if multi-tenant)
   */
  async switchTenant(tenantName: string) {
    try {
      await this.helpers.clickElement(this.tenantSwitcher);
      await this.helpers.clickElement(`option:has-text("${tenantName}"), [data-value="${tenantName}"]`);
      await this.helpers.waitForPageLoad();
    } catch {
      // Tenant switcher might not be available
    }
  }

  /**
   * Verify responsive design on mobile
   */
  async verifyMobileLayout() {
    // Set mobile viewport
    await this.page.setViewportSize({ width: 375, height: 667 });
    
    // Sidebar should be hidden or collapsed on mobile
    const sidebar = this.page.locator(this.sidebar);
    const isHidden = await sidebar.isHidden();
    const hasCollapsedClass = await sidebar.getAttribute('class');
    
    expect(isHidden || hasCollapsedClass?.includes('collapsed')).toBeTruthy();
    
    // Header should be visible
    await expect(this.page.locator(this.header)).toBeVisible();
  }

  /**
   * Verify desktop layout
   */
  async verifyDesktopLayout() {
    // Set desktop viewport
    await this.page.setViewportSize({ width: 1920, height: 1080 });
    
    // Sidebar should be visible
    await expect(this.page.locator(this.sidebar)).toBeVisible();
    
    // Header should be visible
    await expect(this.page.locator(this.header)).toBeVisible();
    
    // Stats cards should be in a row layout
    const statsCards = this.page.locator(this.statsCards);
    if (await statsCards.count() > 0) {
      const firstCard = statsCards.first();
      const lastCard = statsCards.last();
      
      const firstBox = await firstCard.boundingBox();
      const lastBox = await lastCard.boundingBox();
      
      // Cards should be horizontally aligned (same Y position approximately)
      if (firstBox && lastBox) {
        expect(Math.abs(firstBox.y - lastBox.y)).toBeLessThan(50);
      }
    }
  }
}
