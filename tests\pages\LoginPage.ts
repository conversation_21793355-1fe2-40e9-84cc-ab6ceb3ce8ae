import { Page, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';

export class LoginPage {
  private helpers: TestHelpers;

  // Selectors
  private readonly emailInput = 'input[name="email"], input[type="email"]';
  private readonly passwordInput = 'input[name="password"], input[type="password"]';
  private readonly loginButton = 'button[type="submit"], button:has-text("Login")';
  private readonly showPasswordButton = 'button:has([data-testid="eye-icon"]), button:has(.eye)';
  private readonly errorMessage = '.error, [role="alert"], .text-red';
  private readonly loadingSpinner = '.animate-spin, .loading';
  private readonly logoContainer = '.logo, [data-testid="logo"]';
  private readonly forgotPasswordLink = 'a:has-text("Forgot"), a:has-text("Lupa")';

  constructor(private page: Page) {
    this.helpers = new TestHelpers(page);
  }

  /**
   * Navigate to login page
   */
  async goto() {
    await this.page.goto('/');
    await this.helpers.waitForPageLoad();
  }

  /**
   * Verify login page is displayed
   */
  async verifyLoginPageDisplayed() {
    await expect(this.page.locator(this.emailInput)).toBeVisible();
    await expect(this.page.locator(this.passwordInput)).toBeVisible();
    await expect(this.page.locator(this.loginButton)).toBeVisible();
    
    // Verify page title or heading
    await expect(this.page).toHaveTitle(/Login|DentalCare/);
    
    // Check for login form heading
    const headings = ['Login ke DentalCare', 'Login', 'Sign In'];
    let headingFound = false;
    
    for (const heading of headings) {
      try {
        await expect(this.page.locator(`h1:has-text("${heading}"), h2:has-text("${heading}")`)).toBeVisible({ timeout: 2000 });
        headingFound = true;
        break;
      } catch {
        // Continue to next heading
      }
    }
    
    expect(headingFound).toBeTruthy();
  }

  /**
   * Fill email field
   */
  async fillEmail(email: string) {
    await this.helpers.fillField(this.emailInput, email, { clear: true });
  }

  /**
   * Fill password field
   */
  async fillPassword(password: string) {
    await this.helpers.fillField(this.passwordInput, password, { clear: true });
  }

  /**
   * Click login button
   */
  async clickLogin() {
    await this.helpers.clickElement(this.loginButton);
  }

  /**
   * Toggle password visibility
   */
  async togglePasswordVisibility() {
    await this.helpers.clickElement(this.showPasswordButton);
  }

  /**
   * Verify password is visible/hidden
   */
  async verifyPasswordVisibility(visible: boolean) {
    const passwordField = this.page.locator(this.passwordInput);
    const type = await passwordField.getAttribute('type');
    
    if (visible) {
      expect(type).toBe('text');
    } else {
      expect(type).toBe('password');
    }
  }

  /**
   * Perform complete login
   */
  async login(email: string, password: string) {
    await this.fillEmail(email);
    await this.fillPassword(password);
    await this.clickLogin();
    
    // Wait for login to complete (either success or error)
    await Promise.race([
      this.page.waitForURL(/\/(?!login)/, { timeout: 10000 }),
      this.page.waitForSelector(this.errorMessage, { timeout: 10000 })
    ]);
  }

  /**
   * Verify login success (redirected to dashboard)
   */
  async verifyLoginSuccess() {
    // Should be redirected away from login page
    await expect(this.page).not.toHaveURL(/login/);
    
    // Should see dashboard or main app elements
    const dashboardElements = [
      'nav, .sidebar',
      '[data-testid="dashboard"], .dashboard',
      'h1:has-text("Dashboard"), h1:has-text("Beranda")'
    ];
    
    let elementFound = false;
    for (const selector of dashboardElements) {
      try {
        await expect(this.page.locator(selector)).toBeVisible({ timeout: 5000 });
        elementFound = true;
        break;
      } catch {
        // Continue to next element
      }
    }
    
    expect(elementFound).toBeTruthy();
  }

  /**
   * Verify login error
   */
  async verifyLoginError(expectedMessage?: string) {
    await expect(this.page.locator(this.errorMessage)).toBeVisible();
    
    if (expectedMessage) {
      await expect(this.page.locator(this.errorMessage)).toContainText(expectedMessage);
    }
  }

  /**
   * Verify loading state
   */
  async verifyLoadingState() {
    await expect(this.page.locator(this.loadingSpinner)).toBeVisible();
    await expect(this.page.locator(this.loginButton)).toBeDisabled();
  }

  /**
   * Wait for loading to complete
   */
  async waitForLoadingComplete() {
    await this.helpers.waitForLoadingComplete();
    await expect(this.page.locator(this.loginButton)).toBeEnabled();
  }

  /**
   * Click forgot password link
   */
  async clickForgotPassword() {
    await this.helpers.clickElement(this.forgotPasswordLink);
  }

  /**
   * Verify form validation errors
   */
  async verifyValidationErrors() {
    // Try to submit empty form
    await this.clickLogin();
    
    // Check for HTML5 validation or custom validation messages
    const emailField = this.page.locator(this.emailInput);
    const passwordField = this.page.locator(this.passwordInput);
    
    // Check if fields are marked as invalid
    await expect(emailField).toHaveAttribute('required');
    await expect(passwordField).toHaveAttribute('required');
  }

  /**
   * Test invalid email format
   */
  async testInvalidEmailFormat() {
    await this.fillEmail('invalid-email');
    await this.fillPassword('password123');
    await this.clickLogin();
    
    // Should show validation error or prevent submission
    const emailField = this.page.locator(this.emailInput);
    const validity = await emailField.evaluate((el: HTMLInputElement) => el.validity.valid);
    expect(validity).toBeFalsy();
  }

  /**
   * Clear all form fields
   */
  async clearForm() {
    await this.helpers.clearForm('form, .login-form');
  }
}
