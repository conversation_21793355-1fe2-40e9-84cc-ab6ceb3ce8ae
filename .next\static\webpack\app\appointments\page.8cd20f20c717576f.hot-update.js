"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/appointments/page",{

/***/ "(app-pages-browser)/./src/app/appointments/page.tsx":
/*!***************************************!*\
  !*** ./src/app/appointments/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AppointmentsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout/Header */ \"(app-pages-browser)/./src/components/Layout/Header.tsx\");\n/* harmony import */ var _components_Appointments_AppointmentCalendar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Appointments/AppointmentCalendar */ \"(app-pages-browser)/./src/components/Appointments/AppointmentCalendar.tsx\");\n/* harmony import */ var _components_Appointments_AppointmentBookingForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Appointments/AppointmentBookingForm */ \"(app-pages-browser)/./src/components/Appointments/AppointmentBookingForm.tsx\");\n/* harmony import */ var _components_Treatments_TreatmentRecordForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Treatments/TreatmentRecordForm */ \"(app-pages-browser)/./src/components/Treatments/TreatmentRecordForm.tsx\");\n/* harmony import */ var _hooks_useAppointments__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useAppointments */ \"(app-pages-browser)/./src/hooks/useAppointments.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction AppointmentsPage() {\n    _s();\n    const [selectedAppointment, setSelectedAppointment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showBookingForm, setShowBookingForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingAppointment, setEditingAppointment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showTreatmentForm, setShowTreatmentForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [treatmentAppointment, setTreatmentAppointment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const updateAppointmentStatusMutation = (0,_hooks_useAppointments__WEBPACK_IMPORTED_MODULE_6__.useUpdateAppointmentStatus)();\n    const handleBookingSuccess = (appointmentId)=>{\n        setShowBookingForm(false);\n        console.log(\"Appointment created with ID:\", appointmentId);\n    };\n    const handleEditAppointment = ()=>{\n        if (selectedAppointment) {\n            setEditingAppointment(selectedAppointment);\n            setSelectedAppointment(null);\n            setShowBookingForm(true);\n        }\n    };\n    const handleCancelAppointment = async ()=>{\n        if (selectedAppointment && window.confirm(\"Apakah Anda yakin ingin membatalkan appointment ini?\")) {\n            try {\n                await updateAppointmentStatusMutation.mutateAsync({\n                    id: selectedAppointment.id,\n                    status: \"cancelled\",\n                    notes: \"Appointment dibatalkan oleh admin\"\n                });\n                setSelectedAppointment(null);\n            } catch (error) {\n                console.error(\"Error canceling appointment:\", error);\n                alert(\"Gagal membatalkan appointment. Silakan coba lagi.\");\n            }\n        }\n    };\n    const handleStartTreatment = async ()=>{\n        if (selectedAppointment) {\n            // Update appointment status to in-progress\n            try {\n                await updateAppointmentStatusMutation.mutateAsync({\n                    id: selectedAppointment.id,\n                    status: \"in-progress\",\n                    notes: \"Treatment dimulai\"\n                });\n                // Open treatment record form\n                setTreatmentAppointment(selectedAppointment);\n                setSelectedAppointment(null);\n                setShowTreatmentForm(true);\n            } catch (error) {\n                console.error(\"Error starting treatment:\", error);\n                alert(\"Gagal memulai treatment. Silakan coba lagi.\");\n            }\n        }\n    };\n    const handleTreatmentSuccess = ()=>{\n        setShowTreatmentForm(false);\n        setTreatmentAppointment(null);\n        // Optionally update appointment status to completed\n        if (treatmentAppointment) {\n            updateAppointmentStatusMutation.mutateAsync({\n                id: treatmentAppointment.id,\n                status: \"completed\",\n                notes: \"Treatment selesai\"\n            });\n        }\n    };\n    if (showTreatmentForm) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 overflow-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    title: \"Record Treatment\",\n                    subtitle: \"Catat treatment yang telah dilakukan\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Treatments_TreatmentRecordForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        onSuccess: handleTreatmentSuccess,\n                        onCancel: ()=>{\n                            setShowTreatmentForm(false);\n                            setTreatmentAppointment(null);\n                        },\n                        appointmentId: treatmentAppointment === null || treatmentAppointment === void 0 ? void 0 : treatmentAppointment.id,\n                        patientId: treatmentAppointment === null || treatmentAppointment === void 0 ? void 0 : treatmentAppointment.patientId\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n            lineNumber: 85,\n            columnNumber: 7\n        }, this);\n    }\n    if (showBookingForm) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 overflow-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    title: editingAppointment ? \"Edit Appointment\" : \"Buat Appointment Baru\",\n                    subtitle: editingAppointment ? \"Ubah detail appointment\" : \"Jadwalkan appointment untuk pasien\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Appointments_AppointmentBookingForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        onSuccess: (appointmentId)=>{\n                            handleBookingSuccess(appointmentId);\n                            setEditingAppointment(null);\n                        },\n                        onCancel: ()=>{\n                            setShowBookingForm(false);\n                            setEditingAppointment(null);\n                        },\n                        editingAppointment: editingAppointment\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 overflow-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                title: \"Manajemen Jadwal\",\n                subtitle: \"Kelola appointment dan jadwal dokter\"\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Appointments_AppointmentCalendar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        onSelectAppointment: setSelectedAppointment,\n                        onCreateAppointment: ()=>setShowBookingForm(true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    selectedAppointment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg max-w-2xl w-full p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Detail Appointment\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedAppointment(null),\n                                            className: \"text-gray-400 hover:text-gray-600\",\n                                            children: \"✕\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Pasien\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium\",\n                                                            children: selectedAppointment.patientName\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Dokter\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium\",\n                                                            children: [\n                                                                \"Dr. \",\n                                                                selectedAppointment.doctorName\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Tanggal & Waktu\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium\",\n                                                            children: [\n                                                                new Date(selectedAppointment.date).toLocaleDateString(\"id-ID\"),\n                                                                \" - \",\n                                                                selectedAppointment.time\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Durasi\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium\",\n                                                            children: [\n                                                                selectedAppointment.duration,\n                                                                \" menit\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Jenis Treatment\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium\",\n                                                            children: selectedAppointment.type\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"status-badge \".concat(selectedAppointment.status === \"scheduled\" ? \"status-scheduled\" : selectedAppointment.status === \"confirmed\" ? \"status-confirmed\" : selectedAppointment.status === \"in-progress\" ? \"status-in-progress\" : selectedAppointment.status === \"completed\" ? \"status-completed\" : \"status-cancelled\"),\n                                                            children: selectedAppointment.status === \"scheduled\" ? \"Terjadwal\" : selectedAppointment.status === \"confirmed\" ? \"Dikonfirmasi\" : selectedAppointment.status === \"in-progress\" ? \"Berlangsung\" : selectedAppointment.status === \"completed\" ? \"Selesai\" : \"Dibatalkan\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, this),\n                                        selectedAppointment.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Catatan\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-900 bg-gray-50 p-3 rounded-lg\",\n                                                    children: selectedAppointment.notes\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 19\n                                        }, this),\n                                        selectedAppointment.treatmentPlan && selectedAppointment.treatmentPlan.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Rencana Treatment\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2 mt-2\",\n                                                    children: selectedAppointment.treatmentPlan.map((treatment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm\",\n                                                            children: treatment\n                                                        }, index, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleEditAppointment,\n                                            className: \"btn-secondary\",\n                                            disabled: (selectedAppointment === null || selectedAppointment === void 0 ? void 0 : selectedAppointment.status) === \"cancelled\" || (selectedAppointment === null || selectedAppointment === void 0 ? void 0 : selectedAppointment.status) === \"completed\",\n                                            children: \"Edit\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCancelAppointment,\n                                            className: \"btn-danger\",\n                                            disabled: (selectedAppointment === null || selectedAppointment === void 0 ? void 0 : selectedAppointment.status) === \"cancelled\" || (selectedAppointment === null || selectedAppointment === void 0 ? void 0 : selectedAppointment.status) === \"completed\",\n                                            children: \"Batalkan\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleStartTreatment,\n                                            className: \"btn-primary\",\n                                            disabled: (selectedAppointment === null || selectedAppointment === void 0 ? void 0 : selectedAppointment.status) === \"in-progress\" || (selectedAppointment === null || selectedAppointment === void 0 ? void 0 : selectedAppointment.status) === \"completed\" || (selectedAppointment === null || selectedAppointment === void 0 ? void 0 : selectedAppointment.status) === \"cancelled\",\n                                            children: (selectedAppointment === null || selectedAppointment === void 0 ? void 0 : selectedAppointment.status) === \"in-progress\" ? \"Treatment Berlangsung\" : \"Mulai Treatment\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\appointments\\\\page.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n_s(AppointmentsPage, \"yOFx9vwpu34SDZjg0G4acKPABEo=\", false, function() {\n    return [\n        _hooks_useAppointments__WEBPACK_IMPORTED_MODULE_6__.useUpdateAppointmentStatus\n    ];\n});\n_c = AppointmentsPage;\nvar _c;\n$RefreshReg$(_c, \"AppointmentsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/appointments/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Patients/DentalChart.tsx":
/*!*************************************************!*\
  !*** ./src/components/Patients/DentalChart.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DentalChart; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst conditionColors = {\n    healthy: \"fill-white stroke-gray-400\",\n    caries: \"fill-red-200 stroke-red-400\",\n    filled: \"fill-blue-200 stroke-blue-400\",\n    crown: \"fill-yellow-200 stroke-yellow-400\",\n    missing: \"fill-gray-300 stroke-gray-500\",\n    root_canal: \"fill-purple-200 stroke-purple-400\"\n};\nconst conditionLabels = {\n    healthy: \"Sehat\",\n    caries: \"Karies\",\n    filled: \"Tambal\",\n    crown: \"Crown\",\n    missing: \"Hilang\",\n    root_canal: \"Perawatan Saluran Akar\"\n};\nfunction DentalChart(param) {\n    let { patient, onToothUpdate, readOnly = false } = param;\n    _s();\n    const [selectedTooth, setSelectedTooth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showModal, setShowModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [modalCondition, setModalCondition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"healthy\");\n    const [modalNotes, setModalNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Initialize dental chart if not exists\n    const dentalChart = patient.dentalChart || Array.from({\n        length: 32\n    }, (_, i)=>({\n            toothNumber: i + 1,\n            condition: \"healthy\",\n            notes: \"\",\n            updatedAt: new Date().toISOString()\n        }));\n    const getToothCondition = (toothNumber)=>{\n        const tooth = dentalChart.find((t)=>t.toothNumber === toothNumber);\n        return (tooth === null || tooth === void 0 ? void 0 : tooth.condition) || \"healthy\";\n    };\n    const getToothNotes = (toothNumber)=>{\n        const tooth = dentalChart.find((t)=>t.toothNumber === toothNumber);\n        return (tooth === null || tooth === void 0 ? void 0 : tooth.notes) || \"\";\n    };\n    const handleToothClick = (toothNumber)=>{\n        if (readOnly) return;\n        setSelectedTooth(toothNumber);\n        setModalCondition(getToothCondition(toothNumber));\n        setModalNotes(getToothNotes(toothNumber));\n        setShowModal(true);\n    };\n    const handleSaveToothCondition = ()=>{\n        if (selectedTooth && onToothUpdate) {\n            onToothUpdate(selectedTooth, modalCondition, modalNotes);\n        }\n        setShowModal(false);\n        setSelectedTooth(null);\n    };\n    // Tooth numbering: 1-16 upper jaw, 17-32 lower jaw\n    const upperTeeth = Array.from({\n        length: 16\n    }, (_, i)=>i + 1);\n    const lowerTeeth = Array.from({\n        length: 16\n    }, (_, i)=>i + 17);\n    const ToothSVG = (param)=>/*#__PURE__*/ {\n        let { toothNumber, condition } = param;\n        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n            className: \"cursor-pointer transition-all duration-200 hover:opacity-80 \".concat(selectedTooth === toothNumber ? \"ring-2 ring-primary-500\" : \"\"),\n            onClick: ()=>handleToothClick(toothNumber),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                    x: \"0\",\n                    y: \"0\",\n                    width: \"24\",\n                    height: \"32\",\n                    rx: \"4\",\n                    className: conditionColors[condition],\n                    strokeWidth: \"2\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                    x: \"12\",\n                    y: \"20\",\n                    textAnchor: \"middle\",\n                    className: \"text-xs font-medium fill-gray-700\",\n                    style: {\n                        fontSize: \"10px\"\n                    },\n                    children: toothNumber\n                }, void 0, false, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n            lineNumber: 78,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap gap-4 p-4 bg-gray-50 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"w-full text-sm font-medium text-gray-700 mb-2\",\n                        children: \"Keterangan:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    Object.entries(conditionLabels).map((param)=>/*#__PURE__*/ {\n                        let [condition, label] = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-4 h-4 rounded border-2 \".concat(conditionColors[condition])\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: label\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, condition, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this);\n                    })\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Dental Chart\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-gray-700 mb-3\",\n                                        children: \"Rahang Atas\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"450\",\n                                            height: \"50\",\n                                            viewBox: \"0 0 450 50\",\n                                            className: \"border rounded\",\n                                            children: upperTeeth.map((toothNumber, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                                    transform: \"translate(\".concat(index * 28 + 5, \", 9)\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToothSVG, {\n                                                        toothNumber: toothNumber,\n                                                        condition: getToothCondition(toothNumber)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, toothNumber, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-gray-700 mb-3\",\n                                        children: \"Rahang Bawah\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"450\",\n                                            height: \"50\",\n                                            viewBox: \"0 0 450 50\",\n                                            className: \"border rounded\",\n                                            children: lowerTeeth.map((toothNumber, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                                    transform: \"translate(\".concat(index * 28 + 5, \", 9)\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToothSVG, {\n                                                        toothNumber: toothNumber,\n                                                        condition: getToothCondition(toothNumber)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, toothNumber, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    !readOnly && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500 mt-4 text-center\",\n                        children: \"Klik pada gigi untuk mengubah kondisi\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            showModal && selectedTooth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg max-w-md w-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: [\n                                        \"Gigi #\",\n                                        selectedTooth\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowModal(false),\n                                    className: \"text-gray-400 hover:text-gray-600\",\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Kondisi Gigi\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: modalCondition,\n                                            onChange: (e)=>setModalCondition(e.target.value),\n                                            className: \"input\",\n                                            children: Object.entries(conditionLabels).map((param)=>/*#__PURE__*/ {\n                                                let [condition, label] = param;\n                                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: condition,\n                                                    children: label\n                                                }, condition, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 21\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Catatan\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: modalNotes,\n                                            onChange: (e)=>setModalNotes(e.target.value),\n                                            className: \"input min-h-[80px]\",\n                                            placeholder: \"Catatan tambahan untuk gigi ini...\",\n                                            rows: 3\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 p-3 bg-gray-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-6 h-6 rounded border-2 \".concat(conditionColors[modalCondition])\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: conditionLabels[modalCondition]\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowModal(false),\n                                    className: \"btn-secondary\",\n                                    children: \"Batal\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSaveToothCondition,\n                                    className: \"btn-primary\",\n                                    children: \"Simpan\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                lineNumber: 169,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4\",\n                children: Object.entries(conditionLabels).map((param)=>{\n                    let [condition, label] = param;\n                    const count = dentalChart.filter((tooth)=>tooth.condition === condition).length;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 mx-auto mb-2 rounded border-2 \".concat(conditionColors[condition])\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-900\",\n                                children: count\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-600\",\n                                children: label\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, condition, true, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Patients\\\\DentalChart.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n_s(DentalChart, \"jZoW/c76smkjd01GhKXMO2zFPmM=\");\n_c = DentalChart;\nvar _c;\n$RefreshReg$(_c, \"DentalChart\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Patients/DentalChart.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Treatments/TreatmentRecordForm.tsx":
/*!***********************************************************!*\
  !*** ./src/components/Treatments/TreatmentRecordForm.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TreatmentRecordForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_usePatients__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/usePatients */ \"(app-pages-browser)/./src/hooks/usePatients.ts\");\n/* harmony import */ var _hooks_useTreatments__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useTreatments */ \"(app-pages-browser)/./src/hooks/useTreatments.ts\");\n/* harmony import */ var _hooks_useBilling__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useBilling */ \"(app-pages-browser)/./src/hooks/useBilling.ts\");\n/* harmony import */ var _components_Patients_DentalChart__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Patients/DentalChart */ \"(app-pages-browser)/./src/components/Patients/DentalChart.tsx\");\n/* harmony import */ var _barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_PhotoIcon_PlusIcon_TrashIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardDocumentListIcon,CurrencyDollarIcon,PhotoIcon,PlusIcon,TrashIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_PhotoIcon_PlusIcon_TrashIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardDocumentListIcon,CurrencyDollarIcon,PhotoIcon,PlusIcon,TrashIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_PhotoIcon_PlusIcon_TrashIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardDocumentListIcon,CurrencyDollarIcon,PhotoIcon,PlusIcon,TrashIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_PhotoIcon_PlusIcon_TrashIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardDocumentListIcon,CurrencyDollarIcon,PhotoIcon,PlusIcon,TrashIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_PhotoIcon_PlusIcon_TrashIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardDocumentListIcon,CurrencyDollarIcon,PhotoIcon,PlusIcon,TrashIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_PhotoIcon_PlusIcon_TrashIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardDocumentListIcon,CurrencyDollarIcon,PhotoIcon,PlusIcon,TrashIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_PhotoIcon_PlusIcon_TrashIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardDocumentListIcon,CurrencyDollarIcon,PhotoIcon,PlusIcon,TrashIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction TreatmentRecordForm(param) {\n    let { onSuccess, onCancel, appointmentId, patientId: initialPatientId } = param;\n    _s();\n    const { data: patients = [] } = (0,_hooks_usePatients__WEBPACK_IMPORTED_MODULE_2__.usePatients)();\n    const { data: treatments = [] } = (0,_hooks_useTreatments__WEBPACK_IMPORTED_MODULE_3__.useTreatments)();\n    const updateDentalChartMutation = (0,_hooks_usePatients__WEBPACK_IMPORTED_MODULE_2__.useUpdateDentalChart)();\n    const createInvoiceMutation = (0,_hooks_useBilling__WEBPACK_IMPORTED_MODULE_4__.useCreateInvoiceFromAppointment)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        patientId: initialPatientId || \"\",\n        doctorName: \"Dr. Sari Wijaya\",\n        date: new Date().toISOString().split(\"T\")[0],\n        startTime: new Date().toTimeString().slice(0, 5),\n        endTime: \"\",\n        notes: \"\",\n        followUpDate: \"\",\n        createInvoice: true\n    });\n    const [treatmentItems, setTreatmentItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedPatient, setSelectedPatient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [clinicalImages, setClinicalImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Available doctors\n    const doctors = [\n        \"Dr. Sari Wijaya\",\n        \"Dr. Budi Santoso\",\n        \"Dr. Maya Putri\",\n        \"Dr. Ahmad Rahman\"\n    ];\n    // Update selected patient when patientId changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (formData.patientId) {\n            const patient = patients.find((p)=>p.id === formData.patientId);\n            setSelectedPatient(patient || null);\n        }\n    }, [\n        formData.patientId,\n        patients\n    ]);\n    // Calculate end time based on treatment durations\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (formData.startTime && treatmentItems.length > 0) {\n            const totalDuration = treatmentItems.reduce((sum, item)=>sum + item.duration, 0);\n            const startTime = new Date(\"2000-01-01T\".concat(formData.startTime));\n            const endTime = new Date(startTime.getTime() + totalDuration * 60000);\n            setFormData((prev)=>({\n                    ...prev,\n                    endTime: endTime.toTimeString().slice(0, 5)\n                }));\n        }\n    }, [\n        formData.startTime,\n        treatmentItems\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.patientId) newErrors.patientId = \"Pilih pasien\";\n        if (!formData.doctorName) newErrors.doctorName = \"Pilih dokter\";\n        if (!formData.date) newErrors.date = \"Tanggal treatment wajib diisi\";\n        if (!formData.startTime) newErrors.startTime = \"Waktu mulai wajib diisi\";\n        if (treatmentItems.length === 0) newErrors.treatments = \"Minimal satu treatment harus dipilih\";\n        treatmentItems.forEach((item, index)=>{\n            if (!item.treatmentName.trim()) {\n                newErrors[\"treatment_\".concat(index)] = \"Nama treatment wajib diisi\";\n            }\n            if (item.price < 0) {\n                newErrors[\"treatment_\".concat(index, \"_price\")] = \"Harga tidak boleh negatif\";\n            }\n        });\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n    };\n    const addTreatmentItem = ()=>{\n        setTreatmentItems([\n            ...treatmentItems,\n            {\n                treatmentId: \"\",\n                treatmentName: \"\",\n                toothNumbers: [],\n                notes: \"\",\n                price: 0,\n                duration: 30\n            }\n        ]);\n    };\n    const removeTreatmentItem = (index)=>{\n        setTreatmentItems(treatmentItems.filter((_, i)=>i !== index));\n    };\n    const updateTreatmentItem = (index, field, value)=>{\n        const newItems = [\n            ...treatmentItems\n        ];\n        newItems[index] = {\n            ...newItems[index],\n            [field]: value\n        };\n        setTreatmentItems(newItems);\n    };\n    const handleTreatmentSelect = (index, treatmentId)=>{\n        const treatment = treatments.find((t)=>t.id === treatmentId);\n        if (treatment) {\n            updateTreatmentItem(index, \"treatmentId\", treatmentId);\n            updateTreatmentItem(index, \"treatmentName\", treatment.name);\n            updateTreatmentItem(index, \"price\", treatment.price || 0);\n            updateTreatmentItem(index, \"duration\", treatment.estimatedDuration || 30);\n        }\n    };\n    const handleToothUpdate = async (toothNumber, condition, notes)=>{\n        if (!selectedPatient) return;\n        try {\n            await updateDentalChartMutation.mutateAsync({\n                patientId: selectedPatient.id,\n                toothNumber,\n                condition: condition,\n                notes\n            });\n        } catch (error) {\n            console.error(\"Error updating dental chart:\", error);\n        }\n    };\n    const handleImageUpload = (event)=>{\n        const files = event.target.files;\n        if (files) {\n            Array.from(files).forEach((file)=>{\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    var _e_target;\n                    const result = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                    setClinicalImages((prev)=>[\n                            ...prev,\n                            result\n                        ]);\n                };\n                reader.readAsDataURL(file);\n            });\n        }\n    };\n    const removeImage = (index)=>{\n        setClinicalImages((prev)=>prev.filter((_, i)=>i !== index));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        setIsSubmitting(true);\n        try {\n            // Create invoice if requested\n            if (formData.createInvoice && selectedPatient) {\n                const invoiceTreatments = treatmentItems.map((item)=>({\n                        name: item.treatmentName,\n                        price: item.price,\n                        quantity: 1\n                    }));\n                await createInvoiceMutation.mutateAsync({\n                    appointmentId: appointmentId || \"\",\n                    patientId: selectedPatient.id,\n                    patientName: selectedPatient.name,\n                    treatments: invoiceTreatments\n                });\n            }\n            // Here you would typically save the treatment record\n            // For now, we'll just call onSuccess\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n        } catch (error) {\n            console.error(\"Error saving treatment record:\", error);\n            setErrors({\n                submit: \"Gagal menyimpan record treatment. Silakan coba lagi.\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Record Treatment\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-1 text-gray-600\",\n                                    children: \"Catat treatment yang telah dilakukan\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this),\n                        onCancel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: onCancel,\n                            className: \"p-2 hover:bg-gray-100 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_PhotoIcon_PlusIcon_TrashIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-6 h-6 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_PhotoIcon_PlusIcon_TrashIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-5 h-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, this),\n                                \"Informasi Dasar\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Pasien *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.patientId,\n                                            onChange: (e)=>handleInputChange(\"patientId\", e.target.value),\n                                            className: \"input \".concat(errors.patientId ? \"border-red-500\" : \"\"),\n                                            disabled: !!initialPatientId,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Pilih pasien...\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 17\n                                                }, this),\n                                                patients.map((patient)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: patient.id,\n                                                        children: [\n                                                            patient.name,\n                                                            \" - \",\n                                                            patient.medicalRecordNumber\n                                                        ]\n                                                    }, patient.id, true, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.patientId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-600\",\n                                            children: errors.patientId\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Dokter *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.doctorName,\n                                            onChange: (e)=>handleInputChange(\"doctorName\", e.target.value),\n                                            className: \"input \".concat(errors.doctorName ? \"border-red-500\" : \"\"),\n                                            children: doctors.map((doctor)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: doctor,\n                                                    children: doctor\n                                                }, doctor, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.doctorName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-600\",\n                                            children: errors.doctorName\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Tanggal Treatment *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: formData.date,\n                                            onChange: (e)=>handleInputChange(\"date\", e.target.value),\n                                            className: \"input \".concat(errors.date ? \"border-red-500\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-600\",\n                                            children: errors.date\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 31\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Waktu Mulai *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"time\",\n                                            value: formData.startTime,\n                                            onChange: (e)=>handleInputChange(\"startTime\", e.target.value),\n                                            className: \"input \".concat(errors.startTime ? \"border-red-500\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.startTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-600\",\n                                            children: errors.startTime\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Waktu Selesai\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"time\",\n                                            value: formData.endTime,\n                                            onChange: (e)=>handleInputChange(\"endTime\", e.target.value),\n                                            className: \"input\",\n                                            readOnly: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-gray-500\",\n                                            children: \"Otomatis dihitung dari durasi treatment\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Follow-up Date\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: formData.followUpDate,\n                                            onChange: (e)=>handleInputChange(\"followUpDate\", e.target.value),\n                                            className: \"input\",\n                                            min: formData.date\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_PhotoIcon_PlusIcon_TrashIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-5 h-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Treatment yang Dilakukan\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: addTreatmentItem,\n                                    className: \"btn-secondary flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_PhotoIcon_PlusIcon_TrashIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Tambah Treatment\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 11\n                        }, this),\n                        treatmentItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_PhotoIcon_PlusIcon_TrashIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-12 h-12 mx-auto mb-4 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Belum ada treatment yang ditambahkan\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: addTreatmentItem,\n                                    className: \"btn-primary mt-4\",\n                                    children: \"Tambah Treatment Pertama\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: treatmentItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border rounded-lg p-4 bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"lg:col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Treatment\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: item.treatmentId,\n                                                                onChange: (e)=>handleTreatmentSelect(index, e.target.value),\n                                                                className: \"input flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"Pilih treatment...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                                        lineNumber: 395,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    treatments.map((treatment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: treatment.id,\n                                                                            children: treatment.name\n                                                                        }, treatment.id, false, {\n                                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                                            lineNumber: 397,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: item.treatmentName,\n                                                                onChange: (e)=>updateTreatmentItem(index, \"treatmentName\", e.target.value),\n                                                                className: \"input flex-1\",\n                                                                placeholder: \"Atau ketik manual\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Harga (Rp)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: item.price,\n                                                        onChange: (e)=>updateTreatmentItem(index, \"price\", Number(e.target.value)),\n                                                        className: \"input\",\n                                                        min: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-end\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>removeTreatmentItem(index),\n                                                    className: \"p-2 text-red-600 hover:bg-red-50 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_PhotoIcon_PlusIcon_TrashIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"lg:col-span-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Catatan Treatment\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: item.notes,\n                                                        onChange: (e)=>updateTreatmentItem(index, \"notes\", e.target.value),\n                                                        className: \"input min-h-[60px]\",\n                                                        placeholder: \"Catatan detail treatment...\",\n                                                        rows: 2\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 19\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 13\n                        }, this),\n                        errors.treatments && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-red-600\",\n                            children: errors.treatments\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 9\n                }, this),\n                selectedPatient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: [\n                                \"Dental Chart - \",\n                                selectedPatient.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Patients_DentalChart__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            patient: selectedPatient,\n                            onToothUpdate: handleToothUpdate\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                            lineNumber: 464,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_PhotoIcon_PlusIcon_TrashIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"w-5 h-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 13\n                                }, this),\n                                \"Foto Klinis\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Upload Foto\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"file\",\n                                            accept: \"image/*\",\n                                            multiple: true,\n                                            onChange: handleImageUpload,\n                                            className: \"input\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 13\n                                }, this),\n                                clinicalImages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n                                    children: clinicalImages.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: image,\n                                                    alt: \"Clinical \".concat(index + 1),\n                                                    className: \"w-full h-32 object-cover rounded-lg border\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>removeImage(index),\n                                                    className: \"absolute top-2 right-2 p-1 bg-red-600 text-white rounded-full hover:bg-red-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_PhotoIcon_PlusIcon_TrashIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                            lineNumber: 478,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                    lineNumber: 472,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Catatan & Billing\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                            lineNumber: 517,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Catatan Umum\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: formData.notes,\n                                            onChange: (e)=>handleInputChange(\"notes\", e.target.value),\n                                            className: \"input min-h-[100px]\",\n                                            placeholder: \"Catatan umum treatment, kondisi pasien, dll...\",\n                                            rows: 4\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"createInvoice\",\n                                            checked: formData.createInvoice,\n                                            onChange: (e)=>handleInputChange(\"createInvoice\", e.target.checked),\n                                            className: \"w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"createInvoice\",\n                                            className: \"text-sm font-medium text-gray-700 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_PhotoIcon_PlusIcon_TrashIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Buat invoice otomatis\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, this),\n                                formData.createInvoice && treatmentItems.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-50 p-4 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium text-blue-900 mb-2\",\n                                            children: \"Preview Invoice\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1\",\n                                            children: [\n                                                treatmentItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-800\",\n                                                                children: item.treatmentName || \"Treatment\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                                lineNumber: 555,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-900 font-medium\",\n                                                                children: [\n                                                                    \"Rp \",\n                                                                    item.price.toLocaleString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 21\n                                                    }, this)),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-t border-blue-200 pt-2 mt-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm font-medium\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-900\",\n                                                                children: \"Total:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-900\",\n                                                                children: [\n                                                                    \"Rp \",\n                                                                    treatmentItems.reduce((sum, item)=>sum + item.price, 0).toLocaleString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                    lineNumber: 516,\n                    columnNumber: 9\n                }, this),\n                errors.submit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 rounded-md p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600\",\n                        children: errors.submit\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                        lineNumber: 578,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                    lineNumber: 577,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end space-x-4\",\n                    children: [\n                        onCancel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: onCancel,\n                            className: \"btn-secondary\",\n                            disabled: isSubmitting,\n                            children: \"Batal\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                            lineNumber: 585,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"btn-primary\",\n                            disabled: isSubmitting,\n                            children: isSubmitting ? \"Menyimpan...\" : \"Simpan Treatment Record\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n                    lineNumber: 583,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n            lineNumber: 232,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Treatments\\\\TreatmentRecordForm.tsx\",\n        lineNumber: 231,\n        columnNumber: 5\n    }, this);\n}\n_s(TreatmentRecordForm, \"AGogXDzKmK3mJdgYvxkQh+V2D+Y=\", false, function() {\n    return [\n        _hooks_usePatients__WEBPACK_IMPORTED_MODULE_2__.usePatients,\n        _hooks_useTreatments__WEBPACK_IMPORTED_MODULE_3__.useTreatments,\n        _hooks_usePatients__WEBPACK_IMPORTED_MODULE_2__.useUpdateDentalChart,\n        _hooks_useBilling__WEBPACK_IMPORTED_MODULE_4__.useCreateInvoiceFromAppointment\n    ];\n});\n_c = TreatmentRecordForm;\nvar _c;\n$RefreshReg$(_c, \"TreatmentRecordForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1RyZWF0bWVudHMvVHJlYXRtZW50UmVjb3JkRm9ybS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFNEM7QUFDNEI7QUFDbEI7QUFDZTtBQUNUO0FBVXZCO0FBa0J0QixTQUFTYyxvQkFBb0IsS0FLakI7UUFMaUIsRUFDMUNDLFNBQVMsRUFDVEMsUUFBUSxFQUNSQyxhQUFhLEVBQ2JDLFdBQVdDLGdCQUFnQixFQUNGLEdBTGlCOztJQU0xQyxNQUFNLEVBQUVDLE1BQU1DLFdBQVcsRUFBRSxFQUFFLEdBQUduQiwrREFBV0E7SUFDM0MsTUFBTSxFQUFFa0IsTUFBTUUsYUFBYSxFQUFFLEVBQUUsR0FBR2xCLG1FQUFhQTtJQUMvQyxNQUFNbUIsNEJBQTRCcEIsd0VBQW9CQTtJQUN0RCxNQUFNcUIsd0JBQXdCbkIsa0ZBQStCQTtJQUU3RCxNQUFNLENBQUNvQixVQUFVQyxZQUFZLEdBQUcxQiwrQ0FBUUEsQ0FBQztRQUN2Q2tCLFdBQVdDLG9CQUFvQjtRQUMvQlEsWUFBWTtRQUNaQyxNQUFNLElBQUlDLE9BQU9DLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO1FBQzVDQyxXQUFXLElBQUlILE9BQU9JLFlBQVksR0FBR0MsS0FBSyxDQUFDLEdBQUc7UUFDOUNDLFNBQVM7UUFDVEMsT0FBTztRQUNQQyxjQUFjO1FBQ2RDLGVBQWU7SUFDakI7SUFFQSxNQUFNLENBQUNDLGdCQUFnQkMsa0JBQWtCLEdBQUd4QywrQ0FBUUEsQ0FBa0IsRUFBRTtJQUN4RSxNQUFNLENBQUN5QyxpQkFBaUJDLG1CQUFtQixHQUFHMUMsK0NBQVFBLENBQWlCO0lBQ3ZFLE1BQU0sQ0FBQzJDLGdCQUFnQkMsa0JBQWtCLEdBQUc1QywrQ0FBUUEsQ0FBVyxFQUFFO0lBQ2pFLE1BQU0sQ0FBQzZDLFFBQVFDLFVBQVUsR0FBRzlDLCtDQUFRQSxDQUF5QixDQUFDO0lBQzlELE1BQU0sQ0FBQytDLGNBQWNDLGdCQUFnQixHQUFHaEQsK0NBQVFBLENBQUM7SUFFakQsb0JBQW9CO0lBQ3BCLE1BQU1pRCxVQUFVO1FBQ2Q7UUFDQTtRQUNBO1FBQ0E7S0FDRDtJQUVELGlEQUFpRDtJQUNqRGhELGdEQUFTQSxDQUFDO1FBQ1IsSUFBSXdCLFNBQVNQLFNBQVMsRUFBRTtZQUN0QixNQUFNZ0MsVUFBVTdCLFNBQVM4QixJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLEVBQUUsS0FBSzVCLFNBQVNQLFNBQVM7WUFDOUR3QixtQkFBbUJRLFdBQVc7UUFDaEM7SUFDRixHQUFHO1FBQUN6QixTQUFTUCxTQUFTO1FBQUVHO0tBQVM7SUFFakMsa0RBQWtEO0lBQ2xEcEIsZ0RBQVNBLENBQUM7UUFDUixJQUFJd0IsU0FBU08sU0FBUyxJQUFJTyxlQUFlZSxNQUFNLEdBQUcsR0FBRztZQUNuRCxNQUFNQyxnQkFBZ0JoQixlQUFlaUIsTUFBTSxDQUFDLENBQUNDLEtBQUtDLE9BQVNELE1BQU1DLEtBQUtDLFFBQVEsRUFBRTtZQUNoRixNQUFNM0IsWUFBWSxJQUFJSCxLQUFLLGNBQWlDLE9BQW5CSixTQUFTTyxTQUFTO1lBQzNELE1BQU1HLFVBQVUsSUFBSU4sS0FBS0csVUFBVTRCLE9BQU8sS0FBS0wsZ0JBQWdCO1lBQy9EN0IsWUFBWW1DLENBQUFBLE9BQVM7b0JBQ25CLEdBQUdBLElBQUk7b0JBQ1AxQixTQUFTQSxRQUFRRixZQUFZLEdBQUdDLEtBQUssQ0FBQyxHQUFHO2dCQUMzQztRQUNGO0lBQ0YsR0FBRztRQUFDVCxTQUFTTyxTQUFTO1FBQUVPO0tBQWU7SUFFdkMsTUFBTXVCLGVBQWU7UUFDbkIsTUFBTUMsWUFBb0MsQ0FBQztRQUUzQyxJQUFJLENBQUN0QyxTQUFTUCxTQUFTLEVBQUU2QyxVQUFVN0MsU0FBUyxHQUFHO1FBQy9DLElBQUksQ0FBQ08sU0FBU0UsVUFBVSxFQUFFb0MsVUFBVXBDLFVBQVUsR0FBRztRQUNqRCxJQUFJLENBQUNGLFNBQVNHLElBQUksRUFBRW1DLFVBQVVuQyxJQUFJLEdBQUc7UUFDckMsSUFBSSxDQUFDSCxTQUFTTyxTQUFTLEVBQUUrQixVQUFVL0IsU0FBUyxHQUFHO1FBQy9DLElBQUlPLGVBQWVlLE1BQU0sS0FBSyxHQUFHUyxVQUFVekMsVUFBVSxHQUFHO1FBRXhEaUIsZUFBZXlCLE9BQU8sQ0FBQyxDQUFDTixNQUFNTztZQUM1QixJQUFJLENBQUNQLEtBQUtRLGFBQWEsQ0FBQ0MsSUFBSSxJQUFJO2dCQUM5QkosU0FBUyxDQUFDLGFBQW1CLE9BQU5FLE9BQVEsR0FBRztZQUNwQztZQUNBLElBQUlQLEtBQUtVLEtBQUssR0FBRyxHQUFHO2dCQUNsQkwsU0FBUyxDQUFDLGFBQW1CLE9BQU5FLE9BQU0sVUFBUSxHQUFHO1lBQzFDO1FBQ0Y7UUFFQW5CLFVBQVVpQjtRQUNWLE9BQU9NLE9BQU9DLElBQUksQ0FBQ1AsV0FBV1QsTUFBTSxLQUFLO0lBQzNDO0lBRUEsTUFBTWlCLG9CQUFvQixDQUFDQyxPQUFlQztRQUN4Qy9DLFlBQVltQyxDQUFBQSxPQUFTO2dCQUNuQixHQUFHQSxJQUFJO2dCQUNQLENBQUNXLE1BQU0sRUFBRUM7WUFDWDtRQUVBLElBQUk1QixNQUFNLENBQUMyQixNQUFNLEVBQUU7WUFDakIxQixVQUFVZSxDQUFBQSxPQUFTO29CQUNqQixHQUFHQSxJQUFJO29CQUNQLENBQUNXLE1BQU0sRUFBRTtnQkFDWDtRQUNGO0lBQ0Y7SUFFQSxNQUFNRSxtQkFBbUI7UUFDdkJsQyxrQkFBa0I7ZUFBSUQ7WUFBZ0I7Z0JBQ3BDb0MsYUFBYTtnQkFDYlQsZUFBZTtnQkFDZlUsY0FBYyxFQUFFO2dCQUNoQnhDLE9BQU87Z0JBQ1BnQyxPQUFPO2dCQUNQVCxVQUFVO1lBQ1o7U0FBRTtJQUNKO0lBRUEsTUFBTWtCLHNCQUFzQixDQUFDWjtRQUMzQnpCLGtCQUFrQkQsZUFBZXVDLE1BQU0sQ0FBQyxDQUFDQyxHQUFHQyxJQUFNQSxNQUFNZjtJQUMxRDtJQUVBLE1BQU1nQixzQkFBc0IsQ0FBQ2hCLE9BQWVPLE9BQTRCQztRQUN0RSxNQUFNUyxXQUFXO2VBQUkzQztTQUFlO1FBQ3BDMkMsUUFBUSxDQUFDakIsTUFBTSxHQUFHO1lBQ2hCLEdBQUdpQixRQUFRLENBQUNqQixNQUFNO1lBQ2xCLENBQUNPLE1BQU0sRUFBRUM7UUFDWDtRQUNBakMsa0JBQWtCMEM7SUFDcEI7SUFFQSxNQUFNQyx3QkFBd0IsQ0FBQ2xCLE9BQWVVO1FBQzVDLE1BQU1TLFlBQVk5RCxXQUFXNkIsSUFBSSxDQUFDa0MsQ0FBQUEsSUFBS0EsRUFBRWhDLEVBQUUsS0FBS3NCO1FBQ2hELElBQUlTLFdBQVc7WUFDYkgsb0JBQW9CaEIsT0FBTyxlQUFlVTtZQUMxQ00sb0JBQW9CaEIsT0FBTyxpQkFBaUJtQixVQUFVRSxJQUFJO1lBQzFETCxvQkFBb0JoQixPQUFPLFNBQVNtQixVQUFVaEIsS0FBSyxJQUFJO1lBQ3ZEYSxvQkFBb0JoQixPQUFPLFlBQVltQixVQUFVRyxpQkFBaUIsSUFBSTtRQUN4RTtJQUNGO0lBRUEsTUFBTUMsb0JBQW9CLE9BQU9DLGFBQXFCQyxXQUFtQnREO1FBQ3ZFLElBQUksQ0FBQ0ssaUJBQWlCO1FBRXRCLElBQUk7WUFDRixNQUFNbEIsMEJBQTBCb0UsV0FBVyxDQUFDO2dCQUMxQ3pFLFdBQVd1QixnQkFBZ0JZLEVBQUU7Z0JBQzdCb0M7Z0JBQ0FDLFdBQVdBO2dCQUNYdEQ7WUFDRjtRQUNGLEVBQUUsT0FBT3dELE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGdDQUFnQ0E7UUFDaEQ7SUFDRjtJQUVBLE1BQU1FLG9CQUFvQixDQUFDQztRQUN6QixNQUFNQyxRQUFRRCxNQUFNRSxNQUFNLENBQUNELEtBQUs7UUFDaEMsSUFBSUEsT0FBTztZQUNURSxNQUFNQyxJQUFJLENBQUNILE9BQU9oQyxPQUFPLENBQUNvQyxDQUFBQTtnQkFDeEIsTUFBTUMsU0FBUyxJQUFJQztnQkFDbkJELE9BQU9FLE1BQU0sR0FBRyxDQUFDQzt3QkFDQUE7b0JBQWYsTUFBTUMsVUFBU0QsWUFBQUEsRUFBRVAsTUFBTSxjQUFSTyxnQ0FBQUEsVUFBVUMsTUFBTTtvQkFDL0I3RCxrQkFBa0JpQixDQUFBQSxPQUFROytCQUFJQTs0QkFBTTRDO3lCQUFPO2dCQUM3QztnQkFDQUosT0FBT0ssYUFBYSxDQUFDTjtZQUN2QjtRQUNGO0lBQ0Y7SUFFQSxNQUFNTyxjQUFjLENBQUMxQztRQUNuQnJCLGtCQUFrQmlCLENBQUFBLE9BQVFBLEtBQUtpQixNQUFNLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUEsTUFBTWY7SUFDeEQ7SUFFQSxNQUFNMkMsZUFBZSxPQUFPSjtRQUMxQkEsRUFBRUssY0FBYztRQUVoQixJQUFJLENBQUMvQyxnQkFBZ0I7UUFFckJkLGdCQUFnQjtRQUVoQixJQUFJO1lBQ0YsOEJBQThCO1lBQzlCLElBQUl2QixTQUFTYSxhQUFhLElBQUlHLGlCQUFpQjtnQkFDN0MsTUFBTXFFLG9CQUFvQnZFLGVBQWV3RSxHQUFHLENBQUNyRCxDQUFBQSxPQUFTO3dCQUNwRDRCLE1BQU01QixLQUFLUSxhQUFhO3dCQUN4QkUsT0FBT1YsS0FBS1UsS0FBSzt3QkFDakI0QyxVQUFVO29CQUNaO2dCQUVBLE1BQU14RixzQkFBc0JtRSxXQUFXLENBQUM7b0JBQ3RDMUUsZUFBZUEsaUJBQWlCO29CQUNoQ0MsV0FBV3VCLGdCQUFnQlksRUFBRTtvQkFDN0I0RCxhQUFheEUsZ0JBQWdCNkMsSUFBSTtvQkFDakNoRSxZQUFZd0Y7Z0JBQ2Q7WUFDRjtZQUVBLHFEQUFxRDtZQUNyRCxxQ0FBcUM7WUFDckMvRixzQkFBQUEsZ0NBQUFBO1FBQ0YsRUFBRSxPQUFPNkUsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsa0NBQWtDQTtZQUNoRDlDLFVBQVU7Z0JBQUVvRSxRQUFRO1lBQXVEO1FBQzdFLFNBQVU7WUFDUmxFLGdCQUFnQjtRQUNsQjtJQUNGO0lBRUEscUJBQ0UsOERBQUNtRTtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDQztZQUFLQyxVQUFVVjtZQUFjUSxXQUFVOzs4QkFFdEMsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7OzhDQUNDLDhEQUFDSTtvQ0FBR0gsV0FBVTs4Q0FBbUM7Ozs7Ozs4Q0FDakQsOERBQUNoRTtvQ0FBRWdFLFdBQVU7OENBQXFCOzs7Ozs7Ozs7Ozs7d0JBRW5DcEcsMEJBQ0MsOERBQUN3Rzs0QkFDQ0MsTUFBSzs0QkFDTEMsU0FBUzFHOzRCQUNUb0csV0FBVTtzQ0FFViw0RUFBQ3pHLHNMQUFTQTtnQ0FBQ3lHLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU0zQiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDTzs0QkFBR1AsV0FBVTs7OENBQ1osOERBQUM3RyxzTEFBUUE7b0NBQUM2RyxXQUFVOzs7Ozs7Z0NBQWlCOzs7Ozs7O3NDQUl2Qyw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDs7c0RBQ0MsOERBQUNTOzRDQUFNUixXQUFVO3NEQUErQzs7Ozs7O3NEQUdoRSw4REFBQ1M7NENBQ0NwRCxPQUFPaEQsU0FBU1AsU0FBUzs0Q0FDekI0RyxVQUFVLENBQUN0QixJQUFNakMsa0JBQWtCLGFBQWFpQyxFQUFFUCxNQUFNLENBQUN4QixLQUFLOzRDQUM5RDJDLFdBQVcsU0FBa0QsT0FBekN2RSxPQUFPM0IsU0FBUyxHQUFHLG1CQUFtQjs0Q0FDMUQ2RyxVQUFVLENBQUMsQ0FBQzVHOzs4REFFWiw4REFBQzZHO29EQUFPdkQsT0FBTTs4REFBRzs7Ozs7O2dEQUNoQnBELFNBQVMwRixHQUFHLENBQUMsQ0FBQzdELHdCQUNiLDhEQUFDOEU7d0RBQXdCdkQsT0FBT3ZCLFFBQVFHLEVBQUU7OzREQUN2Q0gsUUFBUW9DLElBQUk7NERBQUM7NERBQUlwQyxRQUFRK0UsbUJBQW1COzt1REFEbEMvRSxRQUFRRyxFQUFFOzs7Ozs7Ozs7Ozt3Q0FLMUJSLE9BQU8zQixTQUFTLGtCQUFJLDhEQUFDa0M7NENBQUVnRSxXQUFVO3NEQUE2QnZFLE9BQU8zQixTQUFTOzs7Ozs7Ozs7Ozs7OENBR2pGLDhEQUFDaUc7O3NEQUNDLDhEQUFDUzs0Q0FBTVIsV0FBVTtzREFBK0M7Ozs7OztzREFHaEUsOERBQUNTOzRDQUNDcEQsT0FBT2hELFNBQVNFLFVBQVU7NENBQzFCbUcsVUFBVSxDQUFDdEIsSUFBTWpDLGtCQUFrQixjQUFjaUMsRUFBRVAsTUFBTSxDQUFDeEIsS0FBSzs0Q0FDL0QyQyxXQUFXLFNBQW1ELE9BQTFDdkUsT0FBT2xCLFVBQVUsR0FBRyxtQkFBbUI7c0RBRTFEc0IsUUFBUThELEdBQUcsQ0FBQyxDQUFDbUIsdUJBQ1osOERBQUNGO29EQUFvQnZELE9BQU95RDs4REFDekJBO21EQURVQTs7Ozs7Ozs7Ozt3Q0FLaEJyRixPQUFPbEIsVUFBVSxrQkFBSSw4REFBQ3lCOzRDQUFFZ0UsV0FBVTtzREFBNkJ2RSxPQUFPbEIsVUFBVTs7Ozs7Ozs7Ozs7OzhDQUduRiw4REFBQ3dGOztzREFDQyw4REFBQ1M7NENBQU1SLFdBQVU7c0RBQStDOzs7Ozs7c0RBR2hFLDhEQUFDZTs0Q0FDQ1YsTUFBSzs0Q0FDTGhELE9BQU9oRCxTQUFTRyxJQUFJOzRDQUNwQmtHLFVBQVUsQ0FBQ3RCLElBQU1qQyxrQkFBa0IsUUFBUWlDLEVBQUVQLE1BQU0sQ0FBQ3hCLEtBQUs7NENBQ3pEMkMsV0FBVyxTQUE2QyxPQUFwQ3ZFLE9BQU9qQixJQUFJLEdBQUcsbUJBQW1COzs7Ozs7d0NBRXREaUIsT0FBT2pCLElBQUksa0JBQUksOERBQUN3Qjs0Q0FBRWdFLFdBQVU7c0RBQTZCdkUsT0FBT2pCLElBQUk7Ozs7Ozs7Ozs7Ozs4Q0FHdkUsOERBQUN1Rjs7c0RBQ0MsOERBQUNTOzRDQUFNUixXQUFVO3NEQUErQzs7Ozs7O3NEQUdoRSw4REFBQ2U7NENBQ0NWLE1BQUs7NENBQ0xoRCxPQUFPaEQsU0FBU08sU0FBUzs0Q0FDekI4RixVQUFVLENBQUN0QixJQUFNakMsa0JBQWtCLGFBQWFpQyxFQUFFUCxNQUFNLENBQUN4QixLQUFLOzRDQUM5RDJDLFdBQVcsU0FBa0QsT0FBekN2RSxPQUFPYixTQUFTLEdBQUcsbUJBQW1COzs7Ozs7d0NBRTNEYSxPQUFPYixTQUFTLGtCQUFJLDhEQUFDb0I7NENBQUVnRSxXQUFVO3NEQUE2QnZFLE9BQU9iLFNBQVM7Ozs7Ozs7Ozs7Ozs4Q0FHakYsOERBQUNtRjs7c0RBQ0MsOERBQUNTOzRDQUFNUixXQUFVO3NEQUErQzs7Ozs7O3NEQUdoRSw4REFBQ2U7NENBQ0NWLE1BQUs7NENBQ0xoRCxPQUFPaEQsU0FBU1UsT0FBTzs0Q0FDdkIyRixVQUFVLENBQUN0QixJQUFNakMsa0JBQWtCLFdBQVdpQyxFQUFFUCxNQUFNLENBQUN4QixLQUFLOzRDQUM1RDJDLFdBQVU7NENBQ1ZnQixRQUFROzs7Ozs7c0RBRVYsOERBQUNoRjs0Q0FBRWdFLFdBQVU7c0RBQTZCOzs7Ozs7Ozs7Ozs7OENBRzVDLDhEQUFDRDs7c0RBQ0MsOERBQUNTOzRDQUFNUixXQUFVO3NEQUErQzs7Ozs7O3NEQUdoRSw4REFBQ2U7NENBQ0NWLE1BQUs7NENBQ0xoRCxPQUFPaEQsU0FBU1ksWUFBWTs0Q0FDNUJ5RixVQUFVLENBQUN0QixJQUFNakMsa0JBQWtCLGdCQUFnQmlDLEVBQUVQLE1BQU0sQ0FBQ3hCLEtBQUs7NENBQ2pFMkMsV0FBVTs0Q0FDVmlCLEtBQUs1RyxTQUFTRyxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBTzFCLDhEQUFDdUY7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNPO29DQUFHUCxXQUFVOztzREFDWiw4REFBQzVHLHNMQUF5QkE7NENBQUM0RyxXQUFVOzs7Ozs7d0NBQWlCOzs7Ozs7OzhDQUd4RCw4REFBQ0k7b0NBQ0NDLE1BQUs7b0NBQ0xDLFNBQVNoRDtvQ0FDVDBDLFdBQVU7O3NEQUVWLDhEQUFDeEcsc0xBQVFBOzRDQUFDd0csV0FBVTs7Ozs7O3NEQUNwQiw4REFBQ2tCO3NEQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7d0JBSVQvRixlQUFlZSxNQUFNLEtBQUssa0JBQ3pCLDhEQUFDNkQ7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDNUcsc0xBQXlCQTtvQ0FBQzRHLFdBQVU7Ozs7Ozs4Q0FDckMsOERBQUNoRTs4Q0FBRTs7Ozs7OzhDQUNILDhEQUFDb0U7b0NBQ0NDLE1BQUs7b0NBQ0xDLFNBQVNoRDtvQ0FDVDBDLFdBQVU7OENBQ1g7Ozs7Ozs7Ozs7O2lEQUtILDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDWjdFLGVBQWV3RSxHQUFHLENBQUMsQ0FBQ3JELE1BQU1PLHNCQUN6Qiw4REFBQ2tEO29DQUFnQkMsV0FBVTs4Q0FDekIsNEVBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDUTt3REFBTVIsV0FBVTtrRUFBK0M7Ozs7OztrRUFHaEUsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ1M7Z0VBQ0NwRCxPQUFPZixLQUFLaUIsV0FBVztnRUFDdkJtRCxVQUFVLENBQUN0QixJQUFNckIsc0JBQXNCbEIsT0FBT3VDLEVBQUVQLE1BQU0sQ0FBQ3hCLEtBQUs7Z0VBQzVEMkMsV0FBVTs7a0ZBRVYsOERBQUNZO3dFQUFPdkQsT0FBTTtrRkFBRzs7Ozs7O29FQUNoQm5ELFdBQVd5RixHQUFHLENBQUMsQ0FBQzNCLDBCQUNmLDhEQUFDNEM7NEVBQTBCdkQsT0FBT1csVUFBVS9CLEVBQUU7c0ZBQzNDK0IsVUFBVUUsSUFBSTsyRUFESkYsVUFBVS9CLEVBQUU7Ozs7Ozs7Ozs7OzBFQUs3Qiw4REFBQzhFO2dFQUNDVixNQUFLO2dFQUNMaEQsT0FBT2YsS0FBS1EsYUFBYTtnRUFDekI0RCxVQUFVLENBQUN0QixJQUFNdkIsb0JBQW9CaEIsT0FBTyxpQkFBaUJ1QyxFQUFFUCxNQUFNLENBQUN4QixLQUFLO2dFQUMzRTJDLFdBQVU7Z0VBQ1ZtQixhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBS2xCLDhEQUFDcEI7O2tFQUNDLDhEQUFDUzt3REFBTVIsV0FBVTtrRUFBK0M7Ozs7OztrRUFHaEUsOERBQUNlO3dEQUNDVixNQUFLO3dEQUNMaEQsT0FBT2YsS0FBS1UsS0FBSzt3REFDakIwRCxVQUFVLENBQUN0QixJQUFNdkIsb0JBQW9CaEIsT0FBTyxTQUFTdUUsT0FBT2hDLEVBQUVQLE1BQU0sQ0FBQ3hCLEtBQUs7d0RBQzFFMkMsV0FBVTt3REFDVmlCLEtBQUk7Ozs7Ozs7Ozs7OzswREFJUiw4REFBQ2xCO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDSTtvREFDQ0MsTUFBSztvREFDTEMsU0FBUyxJQUFNN0Msb0JBQW9CWjtvREFDbkNtRCxXQUFVOzhEQUVWLDRFQUFDdkcsdUxBQVNBO3dEQUFDdUcsV0FBVTs7Ozs7Ozs7Ozs7Ozs7OzswREFJekIsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ1E7d0RBQU1SLFdBQVU7a0VBQStDOzs7Ozs7a0VBR2hFLDhEQUFDcUI7d0RBQ0NoRSxPQUFPZixLQUFLdEIsS0FBSzt3REFDakIwRixVQUFVLENBQUN0QixJQUFNdkIsb0JBQW9CaEIsT0FBTyxTQUFTdUMsRUFBRVAsTUFBTSxDQUFDeEIsS0FBSzt3REFDbkUyQyxXQUFVO3dEQUNWbUIsYUFBWTt3REFDWkcsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7O21DQTdESnpFOzs7Ozs7Ozs7O3dCQXNFZnBCLE9BQU92QixVQUFVLGtCQUNoQiw4REFBQzhCOzRCQUFFZ0UsV0FBVTtzQ0FBNkJ2RSxPQUFPdkIsVUFBVTs7Ozs7Ozs7Ozs7O2dCQUs5RG1CLGlDQUNDLDhEQUFDMEU7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDTzs0QkFBR1AsV0FBVTs7Z0NBQTJDO2dDQUN2QzNFLGdCQUFnQjZDLElBQUk7Ozs7Ozs7c0NBRXRDLDhEQUFDaEYsd0VBQVdBOzRCQUNWNEMsU0FBU1Q7NEJBQ1RrRyxlQUFlbkQ7Ozs7Ozs7Ozs7Ozs4QkFNckIsOERBQUMyQjtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNPOzRCQUFHUCxXQUFVOzs4Q0FDWiw4REFBQzFHLHVMQUFTQTtvQ0FBQzBHLFdBQVU7Ozs7OztnQ0FBaUI7Ozs7Ozs7c0NBSXhDLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEOztzREFDQyw4REFBQ1M7NENBQU1SLFdBQVU7c0RBQStDOzs7Ozs7c0RBR2hFLDhEQUFDZTs0Q0FDQ1YsTUFBSzs0Q0FDTG1CLFFBQU87NENBQ1BDLFFBQVE7NENBQ1JmLFVBQVVoQzs0Q0FDVnNCLFdBQVU7Ozs7Ozs7Ozs7OztnQ0FJYnpFLGVBQWVXLE1BQU0sR0FBRyxtQkFDdkIsOERBQUM2RDtvQ0FBSUMsV0FBVTs4Q0FDWnpFLGVBQWVvRSxHQUFHLENBQUMsQ0FBQytCLE9BQU83RSxzQkFDMUIsOERBQUNrRDs0Q0FBZ0JDLFdBQVU7OzhEQUN6Qiw4REFBQzJCO29EQUNDQyxLQUFLRjtvREFDTEcsS0FBSyxZQUFzQixPQUFWaEYsUUFBUTtvREFDekJtRCxXQUFVOzs7Ozs7OERBRVosOERBQUNJO29EQUNDQyxNQUFLO29EQUNMQyxTQUFTLElBQU1mLFlBQVkxQztvREFDM0JtRCxXQUFVOzhEQUVWLDRFQUFDekcsc0xBQVNBO3dEQUFDeUcsV0FBVTs7Ozs7Ozs7Ozs7OzJDQVhmbkQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBcUJwQiw4REFBQ2tEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ087NEJBQUdQLFdBQVU7c0NBQTJDOzs7Ozs7c0NBSXpELDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEOztzREFDQyw4REFBQ1M7NENBQU1SLFdBQVU7c0RBQStDOzs7Ozs7c0RBR2hFLDhEQUFDcUI7NENBQ0NoRSxPQUFPaEQsU0FBU1csS0FBSzs0Q0FDckIwRixVQUFVLENBQUN0QixJQUFNakMsa0JBQWtCLFNBQVNpQyxFQUFFUCxNQUFNLENBQUN4QixLQUFLOzRDQUMxRDJDLFdBQVU7NENBQ1ZtQixhQUFZOzRDQUNaRyxNQUFNOzs7Ozs7Ozs7Ozs7OENBSVYsOERBQUN2QjtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNlOzRDQUNDVixNQUFLOzRDQUNMcEUsSUFBRzs0Q0FDSDZGLFNBQVN6SCxTQUFTYSxhQUFhOzRDQUMvQndGLFVBQVUsQ0FBQ3RCLElBQU1qQyxrQkFBa0IsaUJBQWlCaUMsRUFBRVAsTUFBTSxDQUFDaUQsT0FBTzs0Q0FDcEU5QixXQUFVOzs7Ozs7c0RBRVosOERBQUNROzRDQUFNdUIsU0FBUTs0Q0FBZ0IvQixXQUFVOzs4REFDdkMsOERBQUMzRyx1TEFBa0JBO29EQUFDMkcsV0FBVTs7Ozs7O2dEQUFpQjs7Ozs7Ozs7Ozs7OztnQ0FLbEQzRixTQUFTYSxhQUFhLElBQUlDLGVBQWVlLE1BQU0sR0FBRyxtQkFDakQsOERBQUM2RDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNnQzs0Q0FBR2hDLFdBQVU7c0RBQXlDOzs7Ozs7c0RBQ3ZELDhEQUFDRDs0Q0FBSUMsV0FBVTs7Z0RBQ1o3RSxlQUFld0UsR0FBRyxDQUFDLENBQUNyRCxNQUFNTyxzQkFDekIsOERBQUNrRDt3REFBZ0JDLFdBQVU7OzBFQUN6Qiw4REFBQ2tCO2dFQUFLbEIsV0FBVTswRUFBaUIxRCxLQUFLUSxhQUFhLElBQUk7Ozs7OzswRUFDdkQsOERBQUNvRTtnRUFBS2xCLFdBQVU7O29FQUE0QjtvRUFDdEMxRCxLQUFLVSxLQUFLLENBQUNpRixjQUFjOzs7Ozs7Ozt1REFIdkJwRjs7Ozs7OERBT1osOERBQUNrRDtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDa0I7Z0VBQUtsQixXQUFVOzBFQUFnQjs7Ozs7OzBFQUNoQyw4REFBQ2tCO2dFQUFLbEIsV0FBVTs7b0VBQWdCO29FQUMxQjdFLGVBQWVpQixNQUFNLENBQUMsQ0FBQ0MsS0FBS0MsT0FBU0QsTUFBTUMsS0FBS1UsS0FBSyxFQUFFLEdBQUdpRixjQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0JBVzNGeEcsT0FBT3FFLE1BQU0sa0JBQ1osOERBQUNDO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDaEU7d0JBQUVnRSxXQUFVO2tDQUFnQnZFLE9BQU9xRSxNQUFNOzs7Ozs7Ozs7Ozs4QkFLOUMsOERBQUNDO29CQUFJQyxXQUFVOzt3QkFDWnBHLDBCQUNDLDhEQUFDd0c7NEJBQ0NDLE1BQUs7NEJBQ0xDLFNBQVMxRzs0QkFDVG9HLFdBQVU7NEJBQ1ZXLFVBQVVoRjtzQ0FDWDs7Ozs7O3NDQUlILDhEQUFDeUU7NEJBQ0NDLE1BQUs7NEJBQ0xMLFdBQVU7NEJBQ1ZXLFVBQVVoRjtzQ0FFVEEsZUFBZSxpQkFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTTdDO0dBMWpCd0JqQzs7UUFNVVosMkRBQVdBO1FBQ1RFLCtEQUFhQTtRQUNiRCxvRUFBb0JBO1FBQ3hCRSw4RUFBK0JBOzs7S0FUdkNTIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL1RyZWF0bWVudHMvVHJlYXRtZW50UmVjb3JkRm9ybS50c3g/ODQ4NCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VQYXRpZW50cywgdXNlVXBkYXRlRGVudGFsQ2hhcnQgfSBmcm9tICdAL2hvb2tzL3VzZVBhdGllbnRzJztcbmltcG9ydCB7IHVzZVRyZWF0bWVudHMgfSBmcm9tICdAL2hvb2tzL3VzZVRyZWF0bWVudHMnO1xuaW1wb3J0IHsgdXNlQ3JlYXRlSW52b2ljZUZyb21BcHBvaW50bWVudCB9IGZyb20gJ0AvaG9va3MvdXNlQmlsbGluZyc7XG5pbXBvcnQgRGVudGFsQ2hhcnQgZnJvbSAnQC9jb21wb25lbnRzL1BhdGllbnRzL0RlbnRhbENoYXJ0JztcbmltcG9ydCB7IFBhdGllbnQsIFRyZWF0bWVudCB9IGZyb20gJ0AvdHlwZXMnO1xuaW1wb3J0IHsgXG4gIFVzZXJJY29uLFxuICBDbGlwYm9hcmREb2N1bWVudExpc3RJY29uLFxuICBDdXJyZW5jeURvbGxhckljb24sXG4gIFBob3RvSWNvbixcbiAgWE1hcmtJY29uLFxuICBQbHVzSWNvbixcbiAgVHJhc2hJY29uXG59IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSc7XG5cbmludGVyZmFjZSBUcmVhdG1lbnRSZWNvcmRGb3JtUHJvcHMge1xuICBvblN1Y2Nlc3M/OiAoKSA9PiB2b2lkO1xuICBvbkNhbmNlbD86ICgpID0+IHZvaWQ7XG4gIGFwcG9pbnRtZW50SWQ/OiBzdHJpbmc7XG4gIHBhdGllbnRJZD86IHN0cmluZztcbn1cblxuaW50ZXJmYWNlIFRyZWF0bWVudEl0ZW0ge1xuICB0cmVhdG1lbnRJZDogc3RyaW5nO1xuICB0cmVhdG1lbnROYW1lOiBzdHJpbmc7XG4gIHRvb3RoTnVtYmVyczogbnVtYmVyW107XG4gIG5vdGVzOiBzdHJpbmc7XG4gIHByaWNlOiBudW1iZXI7XG4gIGR1cmF0aW9uOiBudW1iZXI7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFRyZWF0bWVudFJlY29yZEZvcm0oeyBcbiAgb25TdWNjZXNzLCBcbiAgb25DYW5jZWwsXG4gIGFwcG9pbnRtZW50SWQsXG4gIHBhdGllbnRJZDogaW5pdGlhbFBhdGllbnRJZCBcbn06IFRyZWF0bWVudFJlY29yZEZvcm1Qcm9wcykge1xuICBjb25zdCB7IGRhdGE6IHBhdGllbnRzID0gW10gfSA9IHVzZVBhdGllbnRzKCk7XG4gIGNvbnN0IHsgZGF0YTogdHJlYXRtZW50cyA9IFtdIH0gPSB1c2VUcmVhdG1lbnRzKCk7XG4gIGNvbnN0IHVwZGF0ZURlbnRhbENoYXJ0TXV0YXRpb24gPSB1c2VVcGRhdGVEZW50YWxDaGFydCgpO1xuICBjb25zdCBjcmVhdGVJbnZvaWNlTXV0YXRpb24gPSB1c2VDcmVhdGVJbnZvaWNlRnJvbUFwcG9pbnRtZW50KCk7XG4gIFxuICBjb25zdCBbZm9ybURhdGEsIHNldEZvcm1EYXRhXSA9IHVzZVN0YXRlKHtcbiAgICBwYXRpZW50SWQ6IGluaXRpYWxQYXRpZW50SWQgfHwgJycsXG4gICAgZG9jdG9yTmFtZTogJ0RyLiBTYXJpIFdpamF5YScsXG4gICAgZGF0ZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF0sXG4gICAgc3RhcnRUaW1lOiBuZXcgRGF0ZSgpLnRvVGltZVN0cmluZygpLnNsaWNlKDAsIDUpLFxuICAgIGVuZFRpbWU6ICcnLFxuICAgIG5vdGVzOiAnJyxcbiAgICBmb2xsb3dVcERhdGU6ICcnLFxuICAgIGNyZWF0ZUludm9pY2U6IHRydWVcbiAgfSk7XG5cbiAgY29uc3QgW3RyZWF0bWVudEl0ZW1zLCBzZXRUcmVhdG1lbnRJdGVtc10gPSB1c2VTdGF0ZTxUcmVhdG1lbnRJdGVtW10+KFtdKTtcbiAgY29uc3QgW3NlbGVjdGVkUGF0aWVudCwgc2V0U2VsZWN0ZWRQYXRpZW50XSA9IHVzZVN0YXRlPFBhdGllbnQgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2NsaW5pY2FsSW1hZ2VzLCBzZXRDbGluaWNhbEltYWdlc10gPSB1c2VTdGF0ZTxzdHJpbmdbXT4oW10pO1xuICBjb25zdCBbZXJyb3JzLCBzZXRFcnJvcnNdID0gdXNlU3RhdGU8UmVjb3JkPHN0cmluZywgc3RyaW5nPj4oe30pO1xuICBjb25zdCBbaXNTdWJtaXR0aW5nLCBzZXRJc1N1Ym1pdHRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIEF2YWlsYWJsZSBkb2N0b3JzXG4gIGNvbnN0IGRvY3RvcnMgPSBbXG4gICAgJ0RyLiBTYXJpIFdpamF5YScsXG4gICAgJ0RyLiBCdWRpIFNhbnRvc28nLFxuICAgICdEci4gTWF5YSBQdXRyaScsXG4gICAgJ0RyLiBBaG1hZCBSYWhtYW4nXG4gIF07XG5cbiAgLy8gVXBkYXRlIHNlbGVjdGVkIHBhdGllbnQgd2hlbiBwYXRpZW50SWQgY2hhbmdlc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChmb3JtRGF0YS5wYXRpZW50SWQpIHtcbiAgICAgIGNvbnN0IHBhdGllbnQgPSBwYXRpZW50cy5maW5kKHAgPT4gcC5pZCA9PT0gZm9ybURhdGEucGF0aWVudElkKTtcbiAgICAgIHNldFNlbGVjdGVkUGF0aWVudChwYXRpZW50IHx8IG51bGwpO1xuICAgIH1cbiAgfSwgW2Zvcm1EYXRhLnBhdGllbnRJZCwgcGF0aWVudHNdKTtcblxuICAvLyBDYWxjdWxhdGUgZW5kIHRpbWUgYmFzZWQgb24gdHJlYXRtZW50IGR1cmF0aW9uc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChmb3JtRGF0YS5zdGFydFRpbWUgJiYgdHJlYXRtZW50SXRlbXMubGVuZ3RoID4gMCkge1xuICAgICAgY29uc3QgdG90YWxEdXJhdGlvbiA9IHRyZWF0bWVudEl0ZW1zLnJlZHVjZSgoc3VtLCBpdGVtKSA9PiBzdW0gKyBpdGVtLmR1cmF0aW9uLCAwKTtcbiAgICAgIGNvbnN0IHN0YXJ0VGltZSA9IG5ldyBEYXRlKGAyMDAwLTAxLTAxVCR7Zm9ybURhdGEuc3RhcnRUaW1lfWApO1xuICAgICAgY29uc3QgZW5kVGltZSA9IG5ldyBEYXRlKHN0YXJ0VGltZS5nZXRUaW1lKCkgKyB0b3RhbER1cmF0aW9uICogNjAwMDApO1xuICAgICAgc2V0Rm9ybURhdGEocHJldiA9PiAoe1xuICAgICAgICAuLi5wcmV2LFxuICAgICAgICBlbmRUaW1lOiBlbmRUaW1lLnRvVGltZVN0cmluZygpLnNsaWNlKDAsIDUpXG4gICAgICB9KSk7XG4gICAgfVxuICB9LCBbZm9ybURhdGEuc3RhcnRUaW1lLCB0cmVhdG1lbnRJdGVtc10pO1xuXG4gIGNvbnN0IHZhbGlkYXRlRm9ybSA9ICgpID0+IHtcbiAgICBjb25zdCBuZXdFcnJvcnM6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4gPSB7fTtcblxuICAgIGlmICghZm9ybURhdGEucGF0aWVudElkKSBuZXdFcnJvcnMucGF0aWVudElkID0gJ1BpbGloIHBhc2llbic7XG4gICAgaWYgKCFmb3JtRGF0YS5kb2N0b3JOYW1lKSBuZXdFcnJvcnMuZG9jdG9yTmFtZSA9ICdQaWxpaCBkb2t0ZXInO1xuICAgIGlmICghZm9ybURhdGEuZGF0ZSkgbmV3RXJyb3JzLmRhdGUgPSAnVGFuZ2dhbCB0cmVhdG1lbnQgd2FqaWIgZGlpc2knO1xuICAgIGlmICghZm9ybURhdGEuc3RhcnRUaW1lKSBuZXdFcnJvcnMuc3RhcnRUaW1lID0gJ1dha3R1IG11bGFpIHdhamliIGRpaXNpJztcbiAgICBpZiAodHJlYXRtZW50SXRlbXMubGVuZ3RoID09PSAwKSBuZXdFcnJvcnMudHJlYXRtZW50cyA9ICdNaW5pbWFsIHNhdHUgdHJlYXRtZW50IGhhcnVzIGRpcGlsaWgnO1xuXG4gICAgdHJlYXRtZW50SXRlbXMuZm9yRWFjaCgoaXRlbSwgaW5kZXgpID0+IHtcbiAgICAgIGlmICghaXRlbS50cmVhdG1lbnROYW1lLnRyaW0oKSkge1xuICAgICAgICBuZXdFcnJvcnNbYHRyZWF0bWVudF8ke2luZGV4fWBdID0gJ05hbWEgdHJlYXRtZW50IHdhamliIGRpaXNpJztcbiAgICAgIH1cbiAgICAgIGlmIChpdGVtLnByaWNlIDwgMCkge1xuICAgICAgICBuZXdFcnJvcnNbYHRyZWF0bWVudF8ke2luZGV4fV9wcmljZWBdID0gJ0hhcmdhIHRpZGFrIGJvbGVoIG5lZ2F0aWYnO1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgc2V0RXJyb3JzKG5ld0Vycm9ycyk7XG4gICAgcmV0dXJuIE9iamVjdC5rZXlzKG5ld0Vycm9ycykubGVuZ3RoID09PSAwO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUlucHV0Q2hhbmdlID0gKGZpZWxkOiBzdHJpbmcsIHZhbHVlOiBzdHJpbmcgfCBib29sZWFuKSA9PiB7XG4gICAgc2V0Rm9ybURhdGEocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIFtmaWVsZF06IHZhbHVlXG4gICAgfSkpO1xuICAgIFxuICAgIGlmIChlcnJvcnNbZmllbGRdKSB7XG4gICAgICBzZXRFcnJvcnMocHJldiA9PiAoe1xuICAgICAgICAuLi5wcmV2LFxuICAgICAgICBbZmllbGRdOiAnJ1xuICAgICAgfSkpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBhZGRUcmVhdG1lbnRJdGVtID0gKCkgPT4ge1xuICAgIHNldFRyZWF0bWVudEl0ZW1zKFsuLi50cmVhdG1lbnRJdGVtcywge1xuICAgICAgdHJlYXRtZW50SWQ6ICcnLFxuICAgICAgdHJlYXRtZW50TmFtZTogJycsXG4gICAgICB0b290aE51bWJlcnM6IFtdLFxuICAgICAgbm90ZXM6ICcnLFxuICAgICAgcHJpY2U6IDAsXG4gICAgICBkdXJhdGlvbjogMzBcbiAgICB9XSk7XG4gIH07XG5cbiAgY29uc3QgcmVtb3ZlVHJlYXRtZW50SXRlbSA9IChpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgc2V0VHJlYXRtZW50SXRlbXModHJlYXRtZW50SXRlbXMuZmlsdGVyKChfLCBpKSA9PiBpICE9PSBpbmRleCkpO1xuICB9O1xuXG4gIGNvbnN0IHVwZGF0ZVRyZWF0bWVudEl0ZW0gPSAoaW5kZXg6IG51bWJlciwgZmllbGQ6IGtleW9mIFRyZWF0bWVudEl0ZW0sIHZhbHVlOiBhbnkpID0+IHtcbiAgICBjb25zdCBuZXdJdGVtcyA9IFsuLi50cmVhdG1lbnRJdGVtc107XG4gICAgbmV3SXRlbXNbaW5kZXhdID0ge1xuICAgICAgLi4ubmV3SXRlbXNbaW5kZXhdLFxuICAgICAgW2ZpZWxkXTogdmFsdWVcbiAgICB9O1xuICAgIHNldFRyZWF0bWVudEl0ZW1zKG5ld0l0ZW1zKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVUcmVhdG1lbnRTZWxlY3QgPSAoaW5kZXg6IG51bWJlciwgdHJlYXRtZW50SWQ6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IHRyZWF0bWVudCA9IHRyZWF0bWVudHMuZmluZCh0ID0+IHQuaWQgPT09IHRyZWF0bWVudElkKTtcbiAgICBpZiAodHJlYXRtZW50KSB7XG4gICAgICB1cGRhdGVUcmVhdG1lbnRJdGVtKGluZGV4LCAndHJlYXRtZW50SWQnLCB0cmVhdG1lbnRJZCk7XG4gICAgICB1cGRhdGVUcmVhdG1lbnRJdGVtKGluZGV4LCAndHJlYXRtZW50TmFtZScsIHRyZWF0bWVudC5uYW1lKTtcbiAgICAgIHVwZGF0ZVRyZWF0bWVudEl0ZW0oaW5kZXgsICdwcmljZScsIHRyZWF0bWVudC5wcmljZSB8fCAwKTtcbiAgICAgIHVwZGF0ZVRyZWF0bWVudEl0ZW0oaW5kZXgsICdkdXJhdGlvbicsIHRyZWF0bWVudC5lc3RpbWF0ZWREdXJhdGlvbiB8fCAzMCk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVRvb3RoVXBkYXRlID0gYXN5bmMgKHRvb3RoTnVtYmVyOiBudW1iZXIsIGNvbmRpdGlvbjogc3RyaW5nLCBub3Rlcz86IHN0cmluZykgPT4ge1xuICAgIGlmICghc2VsZWN0ZWRQYXRpZW50KSByZXR1cm47XG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IHVwZGF0ZURlbnRhbENoYXJ0TXV0YXRpb24ubXV0YXRlQXN5bmMoe1xuICAgICAgICBwYXRpZW50SWQ6IHNlbGVjdGVkUGF0aWVudC5pZCxcbiAgICAgICAgdG9vdGhOdW1iZXIsXG4gICAgICAgIGNvbmRpdGlvbjogY29uZGl0aW9uIGFzIGFueSxcbiAgICAgICAgbm90ZXNcbiAgICAgIH0pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyBkZW50YWwgY2hhcnQ6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVJbWFnZVVwbG9hZCA9IChldmVudDogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IHtcbiAgICBjb25zdCBmaWxlcyA9IGV2ZW50LnRhcmdldC5maWxlcztcbiAgICBpZiAoZmlsZXMpIHtcbiAgICAgIEFycmF5LmZyb20oZmlsZXMpLmZvckVhY2goZmlsZSA9PiB7XG4gICAgICAgIGNvbnN0IHJlYWRlciA9IG5ldyBGaWxlUmVhZGVyKCk7XG4gICAgICAgIHJlYWRlci5vbmxvYWQgPSAoZSkgPT4ge1xuICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGUudGFyZ2V0Py5yZXN1bHQgYXMgc3RyaW5nO1xuICAgICAgICAgIHNldENsaW5pY2FsSW1hZ2VzKHByZXYgPT4gWy4uLnByZXYsIHJlc3VsdF0pO1xuICAgICAgICB9O1xuICAgICAgICByZWFkZXIucmVhZEFzRGF0YVVSTChmaWxlKTtcbiAgICAgIH0pO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCByZW1vdmVJbWFnZSA9IChpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgc2V0Q2xpbmljYWxJbWFnZXMocHJldiA9PiBwcmV2LmZpbHRlcigoXywgaSkgPT4gaSAhPT0gaW5kZXgpKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVTdWJtaXQgPSBhc3luYyAoZTogUmVhY3QuRm9ybUV2ZW50KSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIFxuICAgIGlmICghdmFsaWRhdGVGb3JtKCkpIHJldHVybjtcblxuICAgIHNldElzU3VibWl0dGluZyh0cnVlKTtcbiAgICBcbiAgICB0cnkge1xuICAgICAgLy8gQ3JlYXRlIGludm9pY2UgaWYgcmVxdWVzdGVkXG4gICAgICBpZiAoZm9ybURhdGEuY3JlYXRlSW52b2ljZSAmJiBzZWxlY3RlZFBhdGllbnQpIHtcbiAgICAgICAgY29uc3QgaW52b2ljZVRyZWF0bWVudHMgPSB0cmVhdG1lbnRJdGVtcy5tYXAoaXRlbSA9PiAoe1xuICAgICAgICAgIG5hbWU6IGl0ZW0udHJlYXRtZW50TmFtZSxcbiAgICAgICAgICBwcmljZTogaXRlbS5wcmljZSxcbiAgICAgICAgICBxdWFudGl0eTogMVxuICAgICAgICB9KSk7XG5cbiAgICAgICAgYXdhaXQgY3JlYXRlSW52b2ljZU11dGF0aW9uLm11dGF0ZUFzeW5jKHtcbiAgICAgICAgICBhcHBvaW50bWVudElkOiBhcHBvaW50bWVudElkIHx8ICcnLFxuICAgICAgICAgIHBhdGllbnRJZDogc2VsZWN0ZWRQYXRpZW50LmlkLFxuICAgICAgICAgIHBhdGllbnROYW1lOiBzZWxlY3RlZFBhdGllbnQubmFtZSxcbiAgICAgICAgICB0cmVhdG1lbnRzOiBpbnZvaWNlVHJlYXRtZW50c1xuICAgICAgICB9KTtcbiAgICAgIH1cblxuICAgICAgLy8gSGVyZSB5b3Ugd291bGQgdHlwaWNhbGx5IHNhdmUgdGhlIHRyZWF0bWVudCByZWNvcmRcbiAgICAgIC8vIEZvciBub3csIHdlJ2xsIGp1c3QgY2FsbCBvblN1Y2Nlc3NcbiAgICAgIG9uU3VjY2Vzcz8uKCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNhdmluZyB0cmVhdG1lbnQgcmVjb3JkOicsIGVycm9yKTtcbiAgICAgIHNldEVycm9ycyh7IHN1Ym1pdDogJ0dhZ2FsIG1lbnlpbXBhbiByZWNvcmQgdHJlYXRtZW50LiBTaWxha2FuIGNvYmEgbGFnaS4nIH0pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc1N1Ym1pdHRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNnhsIG14LWF1dG9cIj5cbiAgICAgIDxmb3JtIG9uU3VibWl0PXtoYW5kbGVTdWJtaXR9IGNsYXNzTmFtZT1cInNwYWNlLXktOFwiPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5SZWNvcmQgVHJlYXRtZW50PC9oMj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1ncmF5LTYwMFwiPkNhdGF0IHRyZWF0bWVudCB5YW5nIHRlbGFoIGRpbGFrdWthbjwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICB7b25DYW5jZWwgJiYgKFxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgb25DbGljaz17b25DYW5jZWx9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiBob3ZlcjpiZy1ncmF5LTEwMCByb3VuZGVkLWxnXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFhNYXJrSWNvbiBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFBhdGllbnQgJiBCYXNpYyBJbmZvICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmRcIj5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItNCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgPFVzZXJJY29uIGNsYXNzTmFtZT1cInctNSBoLTUgbXItMlwiIC8+XG4gICAgICAgICAgICBJbmZvcm1hc2kgRGFzYXJcbiAgICAgICAgICA8L2gzPlxuICAgICAgICAgIFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtMyBnYXAtNFwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgUGFzaWVuICpcbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wYXRpZW50SWR9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVJbnB1dENoYW5nZSgncGF0aWVudElkJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGlucHV0ICR7ZXJyb3JzLnBhdGllbnRJZCA/ICdib3JkZXItcmVkLTUwMCcgOiAnJ31gfVxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXshIWluaXRpYWxQYXRpZW50SWR9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+UGlsaWggcGFzaWVuLi4uPC9vcHRpb24+XG4gICAgICAgICAgICAgICAge3BhdGllbnRzLm1hcCgocGF0aWVudCkgPT4gKFxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e3BhdGllbnQuaWR9IHZhbHVlPXtwYXRpZW50LmlkfT5cbiAgICAgICAgICAgICAgICAgICAge3BhdGllbnQubmFtZX0gLSB7cGF0aWVudC5tZWRpY2FsUmVjb3JkTnVtYmVyfVxuICAgICAgICAgICAgICAgICAgPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICB7ZXJyb3JzLnBhdGllbnRJZCAmJiA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5wYXRpZW50SWR9PC9wPn1cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICBEb2t0ZXIgKlxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmRvY3Rvck5hbWV9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVJbnB1dENoYW5nZSgnZG9jdG9yTmFtZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BpbnB1dCAke2Vycm9ycy5kb2N0b3JOYW1lID8gJ2JvcmRlci1yZWQtNTAwJyA6ICcnfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7ZG9jdG9ycy5tYXAoKGRvY3RvcikgPT4gKFxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e2RvY3Rvcn0gdmFsdWU9e2RvY3Rvcn0+XG4gICAgICAgICAgICAgICAgICAgIHtkb2N0b3J9XG4gICAgICAgICAgICAgICAgICA8L29wdGlvbj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICAgIHtlcnJvcnMuZG9jdG9yTmFtZSAmJiA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5kb2N0b3JOYW1lfTwvcD59XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgVGFuZ2dhbCBUcmVhdG1lbnQgKlxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwiZGF0ZVwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmRhdGV9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVJbnB1dENoYW5nZSgnZGF0ZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BpbnB1dCAke2Vycm9ycy5kYXRlID8gJ2JvcmRlci1yZWQtNTAwJyA6ICcnfWB9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIHtlcnJvcnMuZGF0ZSAmJiA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5kYXRlfTwvcD59XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgV2FrdHUgTXVsYWkgKlxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwidGltZVwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnN0YXJ0VGltZX1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUlucHV0Q2hhbmdlKCdzdGFydFRpbWUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgaW5wdXQgJHtlcnJvcnMuc3RhcnRUaW1lID8gJ2JvcmRlci1yZWQtNTAwJyA6ICcnfWB9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIHtlcnJvcnMuc3RhcnRUaW1lICYmIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLnN0YXJ0VGltZX08L3A+fVxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgIFdha3R1IFNlbGVzYWlcbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cInRpbWVcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5lbmRUaW1lfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW5wdXRDaGFuZ2UoJ2VuZFRpbWUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5wdXRcIlxuICAgICAgICAgICAgICAgIHJlYWRPbmx5XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+T3RvbWF0aXMgZGloaXR1bmcgZGFyaSBkdXJhc2kgdHJlYXRtZW50PC9wPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgIEZvbGxvdy11cCBEYXRlXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJkYXRlXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZm9sbG93VXBEYXRlfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW5wdXRDaGFuZ2UoJ2ZvbGxvd1VwRGF0ZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbnB1dFwiXG4gICAgICAgICAgICAgICAgbWluPXtmb3JtRGF0YS5kYXRlfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBUcmVhdG1lbnQgSXRlbXMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8Q2xpcGJvYXJkRG9jdW1lbnRMaXN0SWNvbiBjbGFzc05hbWU9XCJ3LTUgaC01IG1yLTJcIiAvPlxuICAgICAgICAgICAgICBUcmVhdG1lbnQgeWFuZyBEaWxha3VrYW5cbiAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICBvbkNsaWNrPXthZGRUcmVhdG1lbnRJdGVtfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJidG4tc2Vjb25kYXJ5IGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxQbHVzSWNvbiBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgPHNwYW4+VGFtYmFoIFRyZWF0bWVudDwvc3Bhbj5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAge3RyZWF0bWVudEl0ZW1zLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOCB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgIDxDbGlwYm9hcmREb2N1bWVudExpc3RJY29uIGNsYXNzTmFtZT1cInctMTIgaC0xMiBteC1hdXRvIG1iLTQgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgIDxwPkJlbHVtIGFkYSB0cmVhdG1lbnQgeWFuZyBkaXRhbWJhaGthbjwvcD5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2FkZFRyZWF0bWVudEl0ZW19XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuLXByaW1hcnkgbXQtNFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBUYW1iYWggVHJlYXRtZW50IFBlcnRhbWFcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApIDogKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAge3RyZWF0bWVudEl0ZW1zLm1hcCgoaXRlbSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImJvcmRlciByb3VuZGVkLWxnIHAtNCBiZy1ncmF5LTUwXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTQgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBUcmVhdG1lbnRcbiAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2l0ZW0udHJlYXRtZW50SWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlVHJlYXRtZW50U2VsZWN0KGluZGV4LCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlucHV0IGZsZXgtMVwiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj5QaWxpaCB0cmVhdG1lbnQuLi48L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3RyZWF0bWVudHMubWFwKCh0cmVhdG1lbnQpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIGtleT17dHJlYXRtZW50LmlkfSB2YWx1ZT17dHJlYXRtZW50LmlkfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt0cmVhdG1lbnQubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtpdGVtLnRyZWF0bWVudE5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlVHJlYXRtZW50SXRlbShpbmRleCwgJ3RyZWF0bWVudE5hbWUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlucHV0IGZsZXgtMVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiQXRhdSBrZXRpayBtYW51YWxcIlxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIEhhcmdhIChScClcbiAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17aXRlbS5wcmljZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlVHJlYXRtZW50SXRlbShpbmRleCwgJ3ByaWNlJywgTnVtYmVyKGUudGFyZ2V0LnZhbHVlKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbnB1dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtZW5kXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByZW1vdmVUcmVhdG1lbnRJdGVtKGluZGV4KX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiB0ZXh0LXJlZC02MDAgaG92ZXI6YmctcmVkLTUwIHJvdW5kZWQtbGdcIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxUcmFzaEljb24gY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6Y29sLXNwYW4tNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgQ2F0YXRhbiBUcmVhdG1lbnRcbiAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2l0ZW0ubm90ZXN9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZVRyZWF0bWVudEl0ZW0oaW5kZXgsICdub3RlcycsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlucHV0IG1pbi1oLVs2MHB4XVwiXG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkNhdGF0YW4gZGV0YWlsIHRyZWF0bWVudC4uLlwiXG4gICAgICAgICAgICAgICAgICAgICAgICByb3dzPXsyfVxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIHtlcnJvcnMudHJlYXRtZW50cyAmJiAoXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0yIHRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy50cmVhdG1lbnRzfTwvcD5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRGVudGFsIENoYXJ0ICovfVxuICAgICAgICB7c2VsZWN0ZWRQYXRpZW50ICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmRcIj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+XG4gICAgICAgICAgICAgIERlbnRhbCBDaGFydCAtIHtzZWxlY3RlZFBhdGllbnQubmFtZX1cbiAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICA8RGVudGFsQ2hhcnQgXG4gICAgICAgICAgICAgIHBhdGllbnQ9e3NlbGVjdGVkUGF0aWVudH1cbiAgICAgICAgICAgICAgb25Ub290aFVwZGF0ZT17aGFuZGxlVG9vdGhVcGRhdGV9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBDbGluaWNhbCBJbWFnZXMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZFwiPlxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00IGZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICA8UGhvdG9JY29uIGNsYXNzTmFtZT1cInctNSBoLTUgbXItMlwiIC8+XG4gICAgICAgICAgICBGb3RvIEtsaW5pc1xuICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgIFVwbG9hZCBGb3RvXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJmaWxlXCJcbiAgICAgICAgICAgICAgICBhY2NlcHQ9XCJpbWFnZS8qXCJcbiAgICAgICAgICAgICAgICBtdWx0aXBsZVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbWFnZVVwbG9hZH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbnB1dFwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAge2NsaW5pY2FsSW1hZ2VzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgbWQ6Z3JpZC1jb2xzLTMgbGc6Z3JpZC1jb2xzLTQgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICB7Y2xpbmljYWxJbWFnZXMubWFwKChpbWFnZSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgICAgIHNyYz17aW1hZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgYWx0PXtgQ2xpbmljYWwgJHtpbmRleCArIDF9YH1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC0zMiBvYmplY3QtY292ZXIgcm91bmRlZC1sZyBib3JkZXJcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcmVtb3ZlSW1hZ2UoaW5kZXgpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0yIHJpZ2h0LTIgcC0xIGJnLXJlZC02MDAgdGV4dC13aGl0ZSByb3VuZGVkLWZ1bGwgaG92ZXI6YmctcmVkLTcwMFwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8WE1hcmtJY29uIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBOb3RlcyAmIEludm9pY2UgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZFwiPlxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+XG4gICAgICAgICAgICBDYXRhdGFuICYgQmlsbGluZ1xuICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgIENhdGF0YW4gVW11bVxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEubm90ZXN9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVJbnB1dENoYW5nZSgnbm90ZXMnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5wdXQgbWluLWgtWzEwMHB4XVwiXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJDYXRhdGFuIHVtdW0gdHJlYXRtZW50LCBrb25kaXNpIHBhc2llbiwgZGxsLi4uXCJcbiAgICAgICAgICAgICAgICByb3dzPXs0fVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgaWQ9XCJjcmVhdGVJbnZvaWNlXCJcbiAgICAgICAgICAgICAgICBjaGVja2VkPXtmb3JtRGF0YS5jcmVhdGVJbnZvaWNlfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW5wdXRDaGFuZ2UoJ2NyZWF0ZUludm9pY2UnLCBlLnRhcmdldC5jaGVja2VkKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtcHJpbWFyeS02MDAgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQgZm9jdXM6cmluZy1wcmltYXJ5LTUwMFwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwiY3JlYXRlSW52b2ljZVwiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxDdXJyZW5jeURvbGxhckljb24gY2xhc3NOYW1lPVwidy00IGgtNCBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICBCdWF0IGludm9pY2Ugb3RvbWF0aXNcbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7Zm9ybURhdGEuY3JlYXRlSW52b2ljZSAmJiB0cmVhdG1lbnRJdGVtcy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibHVlLTUwIHAtNCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ibHVlLTkwMCBtYi0yXCI+UHJldmlldyBJbnZvaWNlPC9oND5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICAgICAge3RyZWF0bWVudEl0ZW1zLm1hcCgoaXRlbSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ibHVlLTgwMFwiPntpdGVtLnRyZWF0bWVudE5hbWUgfHwgJ1RyZWF0bWVudCd9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtYmx1ZS05MDAgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIFJwIHtpdGVtLnByaWNlLnRvTG9jYWxlU3RyaW5nKCl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItdCBib3JkZXItYmx1ZS0yMDAgcHQtMiBtdC0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gdGV4dC1zbSBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtYmx1ZS05MDBcIj5Ub3RhbDo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ibHVlLTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgUnAge3RyZWF0bWVudEl0ZW1zLnJlZHVjZSgoc3VtLCBpdGVtKSA9PiBzdW0gKyBpdGVtLnByaWNlLCAwKS50b0xvY2FsZVN0cmluZygpfVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogU3VibWl0IEVycm9yICovfVxuICAgICAgICB7ZXJyb3JzLnN1Ym1pdCAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHJvdW5kZWQtbWQgcC00XCI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLnN1Ym1pdH08L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIEFjdGlvbiBCdXR0b25zICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1lbmQgc3BhY2UteC00XCI+XG4gICAgICAgICAge29uQ2FuY2VsICYmIChcbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e29uQ2FuY2VsfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJidG4tc2Vjb25kYXJ5XCJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzU3VibWl0dGluZ31cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgQmF0YWxcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICl9XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJidG4tcHJpbWFyeVwiXG4gICAgICAgICAgICBkaXNhYmxlZD17aXNTdWJtaXR0aW5nfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtpc1N1Ym1pdHRpbmcgPyAnTWVueWltcGFuLi4uJyA6ICdTaW1wYW4gVHJlYXRtZW50IFJlY29yZCd9XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9mb3JtPlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUGF0aWVudHMiLCJ1c2VVcGRhdGVEZW50YWxDaGFydCIsInVzZVRyZWF0bWVudHMiLCJ1c2VDcmVhdGVJbnZvaWNlRnJvbUFwcG9pbnRtZW50IiwiRGVudGFsQ2hhcnQiLCJVc2VySWNvbiIsIkNsaXBib2FyZERvY3VtZW50TGlzdEljb24iLCJDdXJyZW5jeURvbGxhckljb24iLCJQaG90b0ljb24iLCJYTWFya0ljb24iLCJQbHVzSWNvbiIsIlRyYXNoSWNvbiIsIlRyZWF0bWVudFJlY29yZEZvcm0iLCJvblN1Y2Nlc3MiLCJvbkNhbmNlbCIsImFwcG9pbnRtZW50SWQiLCJwYXRpZW50SWQiLCJpbml0aWFsUGF0aWVudElkIiwiZGF0YSIsInBhdGllbnRzIiwidHJlYXRtZW50cyIsInVwZGF0ZURlbnRhbENoYXJ0TXV0YXRpb24iLCJjcmVhdGVJbnZvaWNlTXV0YXRpb24iLCJmb3JtRGF0YSIsInNldEZvcm1EYXRhIiwiZG9jdG9yTmFtZSIsImRhdGUiLCJEYXRlIiwidG9JU09TdHJpbmciLCJzcGxpdCIsInN0YXJ0VGltZSIsInRvVGltZVN0cmluZyIsInNsaWNlIiwiZW5kVGltZSIsIm5vdGVzIiwiZm9sbG93VXBEYXRlIiwiY3JlYXRlSW52b2ljZSIsInRyZWF0bWVudEl0ZW1zIiwic2V0VHJlYXRtZW50SXRlbXMiLCJzZWxlY3RlZFBhdGllbnQiLCJzZXRTZWxlY3RlZFBhdGllbnQiLCJjbGluaWNhbEltYWdlcyIsInNldENsaW5pY2FsSW1hZ2VzIiwiZXJyb3JzIiwic2V0RXJyb3JzIiwiaXNTdWJtaXR0aW5nIiwic2V0SXNTdWJtaXR0aW5nIiwiZG9jdG9ycyIsInBhdGllbnQiLCJmaW5kIiwicCIsImlkIiwibGVuZ3RoIiwidG90YWxEdXJhdGlvbiIsInJlZHVjZSIsInN1bSIsIml0ZW0iLCJkdXJhdGlvbiIsImdldFRpbWUiLCJwcmV2IiwidmFsaWRhdGVGb3JtIiwibmV3RXJyb3JzIiwiZm9yRWFjaCIsImluZGV4IiwidHJlYXRtZW50TmFtZSIsInRyaW0iLCJwcmljZSIsIk9iamVjdCIsImtleXMiLCJoYW5kbGVJbnB1dENoYW5nZSIsImZpZWxkIiwidmFsdWUiLCJhZGRUcmVhdG1lbnRJdGVtIiwidHJlYXRtZW50SWQiLCJ0b290aE51bWJlcnMiLCJyZW1vdmVUcmVhdG1lbnRJdGVtIiwiZmlsdGVyIiwiXyIsImkiLCJ1cGRhdGVUcmVhdG1lbnRJdGVtIiwibmV3SXRlbXMiLCJoYW5kbGVUcmVhdG1lbnRTZWxlY3QiLCJ0cmVhdG1lbnQiLCJ0IiwibmFtZSIsImVzdGltYXRlZER1cmF0aW9uIiwiaGFuZGxlVG9vdGhVcGRhdGUiLCJ0b290aE51bWJlciIsImNvbmRpdGlvbiIsIm11dGF0ZUFzeW5jIiwiZXJyb3IiLCJjb25zb2xlIiwiaGFuZGxlSW1hZ2VVcGxvYWQiLCJldmVudCIsImZpbGVzIiwidGFyZ2V0IiwiQXJyYXkiLCJmcm9tIiwiZmlsZSIsInJlYWRlciIsIkZpbGVSZWFkZXIiLCJvbmxvYWQiLCJlIiwicmVzdWx0IiwicmVhZEFzRGF0YVVSTCIsInJlbW92ZUltYWdlIiwiaGFuZGxlU3VibWl0IiwicHJldmVudERlZmF1bHQiLCJpbnZvaWNlVHJlYXRtZW50cyIsIm1hcCIsInF1YW50aXR5IiwicGF0aWVudE5hbWUiLCJzdWJtaXQiLCJkaXYiLCJjbGFzc05hbWUiLCJmb3JtIiwib25TdWJtaXQiLCJoMiIsImJ1dHRvbiIsInR5cGUiLCJvbkNsaWNrIiwiaDMiLCJsYWJlbCIsInNlbGVjdCIsIm9uQ2hhbmdlIiwiZGlzYWJsZWQiLCJvcHRpb24iLCJtZWRpY2FsUmVjb3JkTnVtYmVyIiwiZG9jdG9yIiwiaW5wdXQiLCJyZWFkT25seSIsIm1pbiIsInNwYW4iLCJwbGFjZWhvbGRlciIsIk51bWJlciIsInRleHRhcmVhIiwicm93cyIsIm9uVG9vdGhVcGRhdGUiLCJhY2NlcHQiLCJtdWx0aXBsZSIsImltYWdlIiwiaW1nIiwic3JjIiwiYWx0IiwiY2hlY2tlZCIsImh0bWxGb3IiLCJoNCIsInRvTG9jYWxlU3RyaW5nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Treatments/TreatmentRecordForm.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useBilling.ts":
/*!*********************************!*\
  !*** ./src/hooks/useBilling.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreateInvoice: function() { return /* binding */ useCreateInvoice; },\n/* harmony export */   useCreateInvoiceFromAppointment: function() { return /* binding */ useCreateInvoiceFromAppointment; },\n/* harmony export */   useDeleteInvoice: function() { return /* binding */ useDeleteInvoice; },\n/* harmony export */   useInvoice: function() { return /* binding */ useInvoice; },\n/* harmony export */   useInvoices: function() { return /* binding */ useInvoices; },\n/* harmony export */   useInvoicesByPatient: function() { return /* binding */ useInvoicesByPatient; },\n/* harmony export */   useMarkInvoiceAsPaid: function() { return /* binding */ useMarkInvoiceAsPaid; },\n/* harmony export */   usePendingInvoices: function() { return /* binding */ usePendingInvoices; },\n/* harmony export */   useRevenueStats: function() { return /* binding */ useRevenueStats; },\n/* harmony export */   useUpdateInvoice: function() { return /* binding */ useUpdateInvoice; }\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _services_billing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/billing */ \"(app-pages-browser)/./src/services/billing.ts\");\n/* harmony import */ var _services_base_TenantServiceRegistry__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/base/TenantServiceRegistry */ \"(app-pages-browser)/./src/services/base/TenantServiceRegistry.ts\");\n/* harmony import */ var _contexts_TenantContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TenantContext */ \"(app-pages-browser)/./src/contexts/TenantContext.tsx\");\n\n\n\n\n/**\n * Get all invoices\n */ function useInvoices() {\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_2__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)({\n        queryKey: [\n            \"invoices\",\n            tenantId\n        ],\n        queryFn: async ()=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const billingService = _services_base_TenantServiceRegistry__WEBPACK_IMPORTED_MODULE_1__.TenantServiceRegistry.getService(_services_billing__WEBPACK_IMPORTED_MODULE_0__.BillingService, tenantId, \"billing\");\n            return billingService.getInvoices();\n        },\n        enabled: !!tenantId,\n        staleTime: 30000\n    });\n}\n/**\n * Get invoice by ID\n */ function useInvoice(id) {\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_2__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)({\n        queryKey: [\n            \"invoice\",\n            tenantId,\n            id\n        ],\n        queryFn: async ()=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const billingService = _services_base_TenantServiceRegistry__WEBPACK_IMPORTED_MODULE_1__.TenantServiceRegistry.getService(_services_billing__WEBPACK_IMPORTED_MODULE_0__.BillingService, tenantId, \"billing\");\n            return billingService.getInvoice(id);\n        },\n        enabled: !!tenantId && !!id\n    });\n}\n/**\n * Get invoices by patient\n */ function useInvoicesByPatient(patientId) {\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_2__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)({\n        queryKey: [\n            \"invoices\",\n            tenantId,\n            \"patient\",\n            patientId\n        ],\n        queryFn: async ()=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const billingService = _services_base_TenantServiceRegistry__WEBPACK_IMPORTED_MODULE_1__.TenantServiceRegistry.getService(_services_billing__WEBPACK_IMPORTED_MODULE_0__.BillingService, tenantId, \"billing\");\n            return billingService.getInvoicesByPatient(patientId);\n        },\n        enabled: !!tenantId && !!patientId\n    });\n}\n/**\n * Get pending invoices\n */ function usePendingInvoices() {\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_2__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)({\n        queryKey: [\n            \"invoices\",\n            tenantId,\n            \"pending\"\n        ],\n        queryFn: async ()=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const billingService = _services_base_TenantServiceRegistry__WEBPACK_IMPORTED_MODULE_1__.TenantServiceRegistry.getService(_services_billing__WEBPACK_IMPORTED_MODULE_0__.BillingService, tenantId, \"billing\");\n            return billingService.getPendingInvoices();\n        },\n        enabled: !!tenantId,\n        staleTime: 30000\n    });\n}\n/**\n * Get revenue statistics\n */ function useRevenueStats(startDate, endDate) {\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_2__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)({\n        queryKey: [\n            \"revenue-stats\",\n            tenantId,\n            startDate,\n            endDate\n        ],\n        queryFn: async ()=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const billingService = _services_base_TenantServiceRegistry__WEBPACK_IMPORTED_MODULE_1__.TenantServiceRegistry.getService(_services_billing__WEBPACK_IMPORTED_MODULE_0__.BillingService, tenantId, \"billing\");\n            return billingService.getRevenueStats(startDate, endDate);\n        },\n        enabled: !!tenantId && !!startDate && !!endDate,\n        staleTime: 60000\n    });\n}\n/**\n * Create new invoice\n */ function useCreateInvoice() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQueryClient)();\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_2__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({\n        mutationFn: (invoiceData)=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const billingService = _services_base_TenantServiceRegistry__WEBPACK_IMPORTED_MODULE_1__.TenantServiceRegistry.getService(_services_billing__WEBPACK_IMPORTED_MODULE_0__.BillingService, tenantId, \"billing\");\n            return billingService.createInvoice(invoiceData);\n        },\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"invoices\",\n                    tenantId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"revenue-stats\",\n                    tenantId\n                ]\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error creating invoice:\", error);\n        }\n    });\n}\n/**\n * Create invoice from appointment\n */ function useCreateInvoiceFromAppointment() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQueryClient)();\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_2__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({\n        mutationFn: (param)=>{\n            let { appointmentId, patientId, patientName, treatments } = param;\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const billingService = _services_base_TenantServiceRegistry__WEBPACK_IMPORTED_MODULE_1__.TenantServiceRegistry.getService(_services_billing__WEBPACK_IMPORTED_MODULE_0__.BillingService, tenantId, \"billing\");\n            return billingService.createInvoiceFromAppointment(appointmentId, patientId, patientName, treatments);\n        },\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"invoices\",\n                    tenantId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"revenue-stats\",\n                    tenantId\n                ]\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error creating invoice from appointment:\", error);\n        }\n    });\n}\n/**\n * Update invoice\n */ function useUpdateInvoice() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQueryClient)();\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_2__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, updates } = param;\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const billingService = _services_base_TenantServiceRegistry__WEBPACK_IMPORTED_MODULE_1__.TenantServiceRegistry.getService(_services_billing__WEBPACK_IMPORTED_MODULE_0__.BillingService, tenantId, \"billing\");\n            return billingService.updateInvoice(id, updates);\n        },\n        onSuccess: (_, param)=>{\n            let { id } = param;\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"invoices\",\n                    tenantId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"invoice\",\n                    tenantId,\n                    id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"revenue-stats\",\n                    tenantId\n                ]\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error updating invoice:\", error);\n        }\n    });\n}\n/**\n * Mark invoice as paid\n */ function useMarkInvoiceAsPaid() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQueryClient)();\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_2__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, paymentMethod } = param;\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const billingService = _services_base_TenantServiceRegistry__WEBPACK_IMPORTED_MODULE_1__.TenantServiceRegistry.getService(_services_billing__WEBPACK_IMPORTED_MODULE_0__.BillingService, tenantId, \"billing\");\n            return billingService.markAsPaid(id, paymentMethod);\n        },\n        onSuccess: (_, param)=>{\n            let { id } = param;\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"invoices\",\n                    tenantId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"invoice\",\n                    tenantId,\n                    id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"revenue-stats\",\n                    tenantId\n                ]\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error marking invoice as paid:\", error);\n        }\n    });\n}\n/**\n * Delete invoice\n */ function useDeleteInvoice() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQueryClient)();\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_2__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({\n        mutationFn: (id)=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const billingService = _services_base_TenantServiceRegistry__WEBPACK_IMPORTED_MODULE_1__.TenantServiceRegistry.getService(_services_billing__WEBPACK_IMPORTED_MODULE_0__.BillingService, tenantId, \"billing\");\n            return billingService.deleteInvoice(id);\n        },\n        onSuccess: (_, id)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"invoices\",\n                    tenantId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"invoice\",\n                    tenantId,\n                    id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"revenue-stats\",\n                    tenantId\n                ]\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error deleting invoice:\", error);\n        }\n    });\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useBilling.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/billing.ts":
/*!*********************************!*\
  !*** ./src/services/billing.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BillingService: function() { return /* binding */ BillingService; }\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _base_TenantService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./base/TenantService */ \"(app-pages-browser)/./src/services/base/TenantService.ts\");\n\n\nclass BillingService extends _base_TenantService__WEBPACK_IMPORTED_MODULE_1__.TenantService {\n    /**\n   * Create new invoice\n   */ async createInvoice(invoiceData) {\n        try {\n            this.validateTenantAccess();\n            const newInvoice = {\n                ...invoiceData,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)(this.getCollection(\"invoices\"), newInvoice);\n            return docRef.id;\n        } catch (error) {\n            this.handleError(error, \"create invoice\");\n        }\n    }\n    /**\n   * Get invoice by ID\n   */ async getInvoice(id) {\n        try {\n            this.validateTenantAccess();\n            const docRef = this.getDocument(\"invoices\", id);\n            const docSnap = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(docRef);\n            if (docSnap.exists()) {\n                return {\n                    id: docSnap.id,\n                    ...docSnap.data()\n                };\n            }\n            return null;\n        } catch (error) {\n            this.handleError(error, \"get invoice\");\n        }\n    }\n    /**\n   * Get all invoices for current tenant\n   */ async getInvoices() {\n        try {\n            this.validateTenantAccess();\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(this.getCollection(\"invoices\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(\"date\", \"desc\"));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n            return querySnapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data()\n                }));\n        } catch (error) {\n            this.handleError(error, \"get invoices\");\n        }\n    }\n    /**\n   * Get invoices by patient ID\n   */ async getInvoicesByPatient(patientId) {\n        try {\n            this.validateTenantAccess();\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(this.getCollection(\"invoices\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(\"patientId\", \"==\", patientId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(\"date\", \"desc\"));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n            return querySnapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data()\n                }));\n        } catch (error) {\n            this.handleError(error, \"get invoices by patient\");\n        }\n    }\n    /**\n   * Get pending invoices\n   */ async getPendingInvoices() {\n        try {\n            this.validateTenantAccess();\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(this.getCollection(\"invoices\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(\"status\", \"in\", [\n                \"sent\",\n                \"overdue\"\n            ]), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(\"date\", \"desc\"));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n            return querySnapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data()\n                }));\n        } catch (error) {\n            this.handleError(error, \"get pending invoices\");\n        }\n    }\n    /**\n   * Update invoice\n   */ async updateInvoice(id, updates) {\n        try {\n            this.validateTenantAccess();\n            const docRef = this.getDocument(\"invoices\", id);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(docRef, {\n                ...updates,\n                updatedAt: new Date().toISOString()\n            });\n        } catch (error) {\n            this.handleError(error, \"update invoice\");\n        }\n    }\n    /**\n   * Mark invoice as paid\n   */ async markAsPaid(id, paymentMethod) {\n        try {\n            this.validateTenantAccess();\n            const docRef = this.getDocument(\"invoices\", id);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(docRef, {\n                status: \"paid\",\n                paymentMethod,\n                paidDate: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            });\n        } catch (error) {\n            this.handleError(error, \"mark invoice as paid\");\n        }\n    }\n    /**\n   * Delete invoice\n   */ async deleteInvoice(id) {\n        try {\n            this.validateTenantAccess();\n            const docRef = this.getDocument(\"invoices\", id);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.deleteDoc)(docRef);\n        } catch (error) {\n            this.handleError(error, \"delete invoice\");\n        }\n    }\n    /**\n   * Generate invoice number\n   */ async generateInvoiceNumber() {\n        try {\n            this.validateTenantAccess();\n            const today = new Date();\n            const year = today.getFullYear();\n            const month = String(today.getMonth() + 1).padStart(2, \"0\");\n            // Get count of invoices this month\n            const startOfMonth = new Date(year, today.getMonth(), 1);\n            const endOfMonth = new Date(year, today.getMonth() + 1, 0);\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(this.getCollection(\"invoices\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(\"date\", \">=\", startOfMonth.toISOString().split(\"T\")[0]), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(\"date\", \"<=\", endOfMonth.toISOString().split(\"T\")[0]));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n            const count = querySnapshot.size + 1;\n            return \"INV-\".concat(year).concat(month, \"-\").concat(String(count).padStart(4, \"0\"));\n        } catch (error) {\n            this.handleError(error, \"generate invoice number\");\n        }\n    }\n    /**\n   * Calculate invoice totals\n   */ calculateTotals(items) {\n        let discount = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, taxRate = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0.11;\n        const subtotal = items.reduce((sum, item)=>sum + item.quantity * item.price, 0);\n        const discountAmount = subtotal * discount / 100;\n        const taxableAmount = subtotal - discountAmount;\n        const tax = taxableAmount * taxRate;\n        const total = taxableAmount + tax;\n        return {\n            subtotal,\n            tax,\n            total\n        };\n    }\n    /**\n   * Get revenue statistics\n   */ async getRevenueStats(startDate, endDate) {\n        try {\n            this.validateTenantAccess();\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(this.getCollection(\"invoices\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(\"date\", \">=\", startDate), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(\"date\", \"<=\", endDate));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n            const invoices = querySnapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data()\n                }));\n            const totalRevenue = invoices.filter((inv)=>inv.status === \"paid\").reduce((sum, inv)=>sum + inv.total, 0);\n            const paidInvoices = invoices.filter((inv)=>inv.status === \"paid\").length;\n            const pendingInvoices = invoices.filter((inv)=>inv.status === \"sent\").length;\n            const overdueInvoices = invoices.filter((inv)=>inv.status === \"overdue\").length;\n            return {\n                totalRevenue,\n                paidInvoices,\n                pendingInvoices,\n                overdueInvoices\n            };\n        } catch (error) {\n            this.handleError(error, \"get revenue stats\");\n        }\n    }\n    /**\n   * Subscribe to invoices changes\n   */ subscribeToInvoices(callback) {\n        this.validateTenantAccess();\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(this.getCollection(\"invoices\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(\"date\", \"desc\"));\n        return (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.onSnapshot)(q, (snapshot)=>{\n            const invoices = snapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data()\n                }));\n            callback(invoices);\n        });\n    }\n    /**\n   * Create invoice from appointment\n   */ async createInvoiceFromAppointment(appointmentId, patientId, patientName, treatments) {\n        try {\n            this.validateTenantAccess();\n            const invoiceNumber = await this.generateInvoiceNumber();\n            const items = treatments.map((treatment)=>({\n                    description: treatment.name,\n                    quantity: treatment.quantity || 1,\n                    price: treatment.price\n                }));\n            const { subtotal, tax, total } = this.calculateTotals(items);\n            const invoiceData = {\n                invoiceNumber,\n                patientId,\n                patientName,\n                date: new Date().toISOString().split(\"T\")[0],\n                items,\n                subtotal,\n                tax,\n                discount: 0,\n                total,\n                status: \"draft\",\n                appointmentId,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            return await this.createInvoice(invoiceData);\n        } catch (error) {\n            this.handleError(error, \"create invoice from appointment\");\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/billing.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ClipboardDocumentListIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ClipboardDocumentListIcon);\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction CurrencyDollarIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CurrencyDollarIcon);\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction PhotoIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(PhotoIcon);\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction TrashIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(TrashIcon);\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\n"));

/***/ })

});