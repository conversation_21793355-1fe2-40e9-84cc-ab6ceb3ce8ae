/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/profile/page";
exports.ids = ["app/profile/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=D%3A%5Cdentalcare.id%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cdentalcare.id&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=D%3A%5Cdentalcare.id%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cdentalcare.id&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'profile',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/profile/page.tsx */ \"(rsc)/./src/app/profile/page.tsx\")), \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\dentalcare.id\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/profile/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/profile/page\",\n        pathname: \"/profile\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZwcm9maWxlJTJGcGFnZSZwYWdlPSUyRnByb2ZpbGUlMkZwYWdlJmFwcFBhdGhzPSUyRnByb2ZpbGUlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcHJvZmlsZSUyRnBhZ2UudHN4JmFwcERpcj1EJTNBJTVDZGVudGFsY2FyZS5pZCU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9RCUzQSU1Q2RlbnRhbGNhcmUuaWQmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxhQUFhLHNCQUFzQjtBQUNpRTtBQUNyQztBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakMsdUJBQXVCLGdLQUFtRjtBQUMxRztBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLHlCQUF5QixvSkFBNEU7QUFDckcsb0JBQW9CLDBOQUFnRjtBQUNwRztBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFDNkQ7QUFDcEYsNkJBQTZCLG1CQUFtQjtBQUNoRDtBQUNPO0FBQ0E7QUFDUDtBQUNBO0FBQ0E7QUFDdUQ7QUFDdkQ7QUFDTyx3QkFBd0IsOEdBQWtCO0FBQ2pEO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsid2VicGFjazovL2RlbnRhbC1jbGluaWMtdWkvPzM4YWYiXSwic291cmNlc0NvbnRlbnQiOlsiXCJUVVJCT1BBQ0sgeyB0cmFuc2l0aW9uOiBuZXh0LXNzciB9XCI7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAncHJvZmlsZScsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFsnX19QQUdFX18nLCB7fSwge1xuICAgICAgICAgIHBhZ2U6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGRlbnRhbGNhcmUuaWRcXFxcc3JjXFxcXGFwcFxcXFxwcm9maWxlXFxcXHBhZ2UudHN4XCIpLCBcIkQ6XFxcXGRlbnRhbGNhcmUuaWRcXFxcc3JjXFxcXGFwcFxcXFxwcm9maWxlXFxcXHBhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICBcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdsYXlvdXQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxkZW50YWxjYXJlLmlkXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKSwgXCJEOlxcXFxkZW50YWxjYXJlLmlkXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCIpLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiRDpcXFxcZGVudGFsY2FyZS5pZFxcXFxzcmNcXFxcYXBwXFxcXHByb2ZpbGVcXFxccGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIjtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9wcm9maWxlL3BhZ2VcIjtcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvcHJvZmlsZS9wYWdlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9wcm9maWxlXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCIsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=D%3A%5Cdentalcare.id%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cdentalcare.id&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cproviders.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cproviders.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q2RlbnRhbGNhcmUuaWQlNUNzcmMlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUQlM0ElNUNkZW50YWxjYXJlLmlkJTVDc3JjJTVDYXBwJTVDcHJvdmlkZXJzLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZW50YWwtY2xpbmljLXVpLz8xYWFlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcZGVudGFsY2FyZS5pZFxcXFxzcmNcXFxcYXBwXFxcXHByb3ZpZGVycy50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cproviders.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cprofile%5Cpage.tsx&server=true!":
/*!********************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cprofile%5Cpage.tsx&server=true! ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/profile/page.tsx */ \"(ssr)/./src/app/profile/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q2RlbnRhbGNhcmUuaWQlNUNzcmMlNUNhcHAlNUNwcm9maWxlJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVudGFsLWNsaW5pYy11aS8/YWMzOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGRlbnRhbGNhcmUuaWRcXFxcc3JjXFxcXGFwcFxcXFxwcm9maWxlXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cprofile%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/profile/page.tsx":
/*!**********************************!*\
  !*** ./src/app/profile/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProfilePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout/Header */ \"(ssr)/./src/components/Layout/Header.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_KeyIcon_PhotoIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,KeyIcon,PhotoIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_KeyIcon_PhotoIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,KeyIcon,PhotoIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_KeyIcon_PhotoIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,KeyIcon,PhotoIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_KeyIcon_PhotoIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,KeyIcon,PhotoIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_KeyIcon_PhotoIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,KeyIcon,PhotoIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ProfilePage() {\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChangingPassword, setIsChangingPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Mock user data - in real app, this would come from auth context\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"Dr. Sarah Putri\",\n        email: \"<EMAIL>\",\n        role: \"Dokter Gigi\",\n        phone: \"08123456789\",\n        avatar: null,\n        joinDate: \"2023-01-15\"\n    });\n    const [editData, setEditData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(userData);\n    const [passwordData, setPasswordData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPassword: \"\",\n        newPassword: \"\",\n        confirmPassword: \"\"\n    });\n    const handleSaveProfile = ()=>{\n        // In real app, this would call an API to update user profile\n        setUserData(editData);\n        setIsEditing(false);\n        alert(\"Profil berhasil diperbarui!\");\n    };\n    const handleCancelEdit = ()=>{\n        setEditData(userData);\n        setIsEditing(false);\n    };\n    const handleChangePassword = ()=>{\n        if (passwordData.newPassword !== passwordData.confirmPassword) {\n            alert(\"Password baru dan konfirmasi password tidak cocok!\");\n            return;\n        }\n        if (passwordData.newPassword.length < 6) {\n            alert(\"Password baru minimal 6 karakter!\");\n            return;\n        }\n        // In real app, this would call an API to change password\n        alert(\"Password berhasil diubah!\");\n        setPasswordData({\n            currentPassword: \"\",\n            newPassword: \"\",\n            confirmPassword: \"\"\n        });\n        setIsChangingPassword(false);\n    };\n    const handleImageUpload = (event)=>{\n        const file = event.target.files?.[0];\n        if (file) {\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                const result = e.target?.result;\n                setEditData((prev)=>({\n                        ...prev,\n                        avatar: result\n                    }));\n            };\n            reader.readAsDataURL(file);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 overflow-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                title: \"Profil Saya\",\n                subtitle: \"Kelola informasi profil dan pengaturan akun\"\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_KeyIcon_PhotoIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"w-5 h-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Informasi Profil\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsEditing(true),\n                                            className: \"btn-secondary\",\n                                            children: \"Edit Profil\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleSaveProfile,\n                                                    className: \"btn-primary flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_KeyIcon_PhotoIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 111,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Simpan\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 112,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleCancelEdit,\n                                                    className: \"btn-secondary flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_KeyIcon_PhotoIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Batal\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-24 rounded-full bg-primary-100 flex items-center justify-center overflow-hidden\",\n                                                    children: (isEditing ? editData.avatar : userData.avatar) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: isEditing ? editData.avatar : userData.avatar,\n                                                        alt: \"Avatar\",\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_KeyIcon_PhotoIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"w-12 h-12 text-primary-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"btn-secondary cursor-pointer\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_KeyIcon_PhotoIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 142,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Upload Foto\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"file\",\n                                                                    accept: \"image/*\",\n                                                                    onChange: handleImageUpload,\n                                                                    className: \"hidden\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 144,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 mt-2\",\n                                                            children: \"JPG, PNG atau GIF. Maksimal 2MB.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Nama Lengkap\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: editData.name,\n                                                            onChange: (e)=>setEditData((prev)=>({\n                                                                        ...prev,\n                                                                        name: e.target.value\n                                                                    })),\n                                                            className: \"input\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-900 py-2\",\n                                                            children: userData.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Email\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"email\",\n                                                            value: editData.email,\n                                                            onChange: (e)=>setEditData((prev)=>({\n                                                                        ...prev,\n                                                                        email: e.target.value\n                                                                    })),\n                                                            className: \"input\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-900 py-2\",\n                                                            children: userData.email\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Nomor Telepon\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"tel\",\n                                                            value: editData.phone,\n                                                            onChange: (e)=>setEditData((prev)=>({\n                                                                        ...prev,\n                                                                        phone: e.target.value\n                                                                    })),\n                                                            className: \"input\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-900 py-2\",\n                                                            children: userData.phone\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-900 py-2\",\n                                                            children: userData.role\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Bergabung Sejak\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-900 py-2\",\n                                                            children: new Date(userData.joinDate).toLocaleDateString(\"id-ID\", {\n                                                                year: \"numeric\",\n                                                                month: \"long\",\n                                                                day: \"numeric\"\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_KeyIcon_PhotoIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Ubah Password\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isChangingPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsChangingPassword(true),\n                                            className: \"btn-secondary\",\n                                            children: \"Ubah Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleChangePassword,\n                                                    className: \"btn-primary flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_KeyIcon_PhotoIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Simpan\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        setIsChangingPassword(false);\n                                                        setPasswordData({\n                                                            currentPassword: \"\",\n                                                            newPassword: \"\",\n                                                            confirmPassword: \"\"\n                                                        });\n                                                    },\n                                                    className: \"btn-secondary flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_KeyIcon_PhotoIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Batal\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this),\n                                isChangingPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Password Saat Ini\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"password\",\n                                                    value: passwordData.currentPassword,\n                                                    onChange: (e)=>setPasswordData((prev)=>({\n                                                                ...prev,\n                                                                currentPassword: e.target.value\n                                                            })),\n                                                    className: \"input\",\n                                                    placeholder: \"Masukkan password saat ini\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Password Baru\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"password\",\n                                                    value: passwordData.newPassword,\n                                                    onChange: (e)=>setPasswordData((prev)=>({\n                                                                ...prev,\n                                                                newPassword: e.target.value\n                                                            })),\n                                                    className: \"input\",\n                                                    placeholder: \"Masukkan password baru\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Konfirmasi Password Baru\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"password\",\n                                                    value: passwordData.confirmPassword,\n                                                    onChange: (e)=>setPasswordData((prev)=>({\n                                                                ...prev,\n                                                                confirmPassword: e.target.value\n                                                            })),\n                                                    className: \"input\",\n                                                    placeholder: \"Konfirmasi password baru\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Untuk keamanan akun, ubah password secara berkala.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: \"Informasi Akun\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center py-3 border-b border-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"Status Akun\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Akun Anda saat ini aktif\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"status-badge bg-green-100 text-green-800\",\n                                                    children: \"Aktif\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center py-3 border-b border-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"Login Terakhir\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Informasi login terakhir\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        new Date().toLocaleDateString(\"id-ID\"),\n                                                        \" - \",\n                                                        new Date().toLocaleTimeString(\"id-ID\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center py-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"Keamanan\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Pengaturan keamanan akun\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-primary-600 hover:text-primary-700 text-sm font-medium\",\n                                                    children: \"Kelola Keamanan\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\profile\\\\page.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/app/profile/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_TenantContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TenantContext */ \"(ssr)/./src/contexts/TenantContext.tsx\");\n/* harmony import */ var _lib_queryClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/queryClient */ \"(ssr)/./src/lib/queryClient.ts\");\n/* harmony import */ var _components_Layout_AppLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Layout/AppLayout */ \"(ssr)/./src/components/Layout/AppLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n// import { ReactQueryDevtools } from '@tanstack/react-query-devtools';\n\n\n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClientProvider, {\n        client: _lib_queryClient__WEBPACK_IMPORTED_MODULE_3__.queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_2__.TenantProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_AppLayout__WEBPACK_IMPORTED_MODULE_4__.AppLayout, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\providers.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\providers.tsx\",\n                lineNumber: 14,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\providers.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\providers.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRTREO0FBQzVELHVFQUF1RTtBQUNqQjtBQUNJO0FBQ1Y7QUFDVTtBQUVuRCxTQUFTSyxVQUFVLEVBQUVDLFFBQVEsRUFBaUM7SUFDbkUscUJBQ0UsOERBQUNOLHNFQUFtQkE7UUFBQ08sUUFBUUoseURBQVdBO2tCQUN0Qyw0RUFBQ0YsK0RBQVlBO3NCQUNYLDRFQUFDQyxtRUFBY0E7MEJBQ2IsNEVBQUNFLG1FQUFTQTs4QkFDUEU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVNiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVudGFsLWNsaW5pYy11aS8uL3NyYy9hcHAvcHJvdmlkZXJzLnRzeD85MzI2Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgUXVlcnlDbGllbnRQcm92aWRlciB9IGZyb20gJ0B0YW5zdGFjay9yZWFjdC1xdWVyeSc7XG4vLyBpbXBvcnQgeyBSZWFjdFF1ZXJ5RGV2dG9vbHMgfSBmcm9tICdAdGFuc3RhY2svcmVhY3QtcXVlcnktZGV2dG9vbHMnO1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCc7XG5pbXBvcnQgeyBUZW5hbnRQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dHMvVGVuYW50Q29udGV4dCc7XG5pbXBvcnQgeyBxdWVyeUNsaWVudCB9IGZyb20gJ0AvbGliL3F1ZXJ5Q2xpZW50JztcbmltcG9ydCB7IEFwcExheW91dCB9IGZyb20gJ0AvY29tcG9uZW50cy9MYXlvdXQvQXBwTGF5b3V0JztcblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIHJldHVybiAoXG4gICAgPFF1ZXJ5Q2xpZW50UHJvdmlkZXIgY2xpZW50PXtxdWVyeUNsaWVudH0+XG4gICAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgICA8VGVuYW50UHJvdmlkZXI+XG4gICAgICAgICAgPEFwcExheW91dD5cbiAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICA8L0FwcExheW91dD5cbiAgICAgICAgPC9UZW5hbnRQcm92aWRlcj5cbiAgICAgIDwvQXV0aFByb3ZpZGVyPlxuICAgICAgey8qIHtwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50JyAmJiAoXG4gICAgICAgIDxSZWFjdFF1ZXJ5RGV2dG9vbHMgaW5pdGlhbElzT3Blbj17ZmFsc2V9IC8+XG4gICAgICApfSAqL31cbiAgICA8L1F1ZXJ5Q2xpZW50UHJvdmlkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUXVlcnlDbGllbnRQcm92aWRlciIsIkF1dGhQcm92aWRlciIsIlRlbmFudFByb3ZpZGVyIiwicXVlcnlDbGllbnQiLCJBcHBMYXlvdXQiLCJQcm92aWRlcnMiLCJjaGlsZHJlbiIsImNsaWVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Auth/LoginForm.tsx":
/*!*******************************************!*\
  !*** ./src/components/Auth/LoginForm.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoginForm: () => (/* binding */ LoginForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* __next_internal_client_entry_do_not_use__ LoginForm auto */ \n\n\n\n\n// Demo data setup removed\nfunction LoginForm() {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [setupLoading, setSetupLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { signIn } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError(\"\");\n        try {\n            await signIn(email, password);\n            router.push(\"/\");\n        } catch (error) {\n            setError(getErrorMessage(error.message));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getErrorMessage = (errorCode)=>{\n        switch(errorCode){\n            case \"auth/user-not-found\":\n                return \"Email tidak terdaftar\";\n            case \"auth/wrong-password\":\n                return \"Password salah\";\n            case \"auth/invalid-email\":\n                return \"Format email tidak valid\";\n            case \"auth/user-disabled\":\n                return \"Akun telah dinonaktifkan\";\n            case \"auth/too-many-requests\":\n                return \"Terlalu banyak percobaan login. Coba lagi nanti\";\n            default:\n                return \"Terjadi kesalahan saat login\";\n        }\n    };\n    // Demo accounts for testing\n    const demoAccounts = [\n        {\n            email: \"<EMAIL>\",\n            password: \"demo123\",\n            role: \"Dokter\"\n        },\n        {\n            email: \"<EMAIL>\",\n            password: \"demo123\",\n            role: \"Resepsionis\"\n        },\n        {\n            email: \"<EMAIL>\",\n            password: \"demo123\",\n            role: \"Admin\"\n        }\n    ];\n    const handleDemoLogin = (demoEmail, demoPassword)=>{\n        setEmail(demoEmail);\n        setPassword(demoPassword);\n    };\n    const handleSetupDemo = async ()=>{\n        setSetupLoading(true);\n        setError(\"\");\n        try {\n            // Demo data setup functionality removed\n            // Real data is now managed through Firebase services\n            alert(\"Demo data setup tidak tersedia. Gunakan akun demo yang sudah ada atau buat data melalui aplikasi.\");\n        } catch (error) {\n            setError(\"Error: \" + error.message);\n        } finally{\n            setSetupLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-primary-100 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto h-16 w-16 bg-primary-600 rounded-xl flex items-center justify-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white font-bold text-2xl\",\n                                children: \"DC\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"Login ke DentalCare\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-gray-600\",\n                            children: \"Sistem Manajemen Klinik Gigi\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-50 border border-green-200 rounded-lg p-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-green-900 mb-2\",\n                            children: \"Demo Mode:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSetupDemo,\n                            disabled: setupLoading,\n                            className: \"w-full bg-green-600 hover:bg-green-700 text-white text-sm py-2 px-4 rounded disabled:opacity-50\",\n                            children: setupLoading ? \"Checking...\" : \"Info Demo Data\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-green-700 mt-1\",\n                            children: \"Aplikasi menggunakan data real dari Firebase. Gunakan akun demo di bawah untuk testing.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-blue-900 mb-2\",\n                            children: \"Demo Accounts:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1\",\n                            children: demoAccounts.map((account, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleDemoLogin(account.email, account.password),\n                                    className: \"block w-full text-left text-xs text-blue-700 hover:text-blue-900 hover:bg-blue-100 px-2 py-1 rounded\",\n                                    children: [\n                                        account.role,\n                                        \": \",\n                                        account.email\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mt-8 space-y-6\",\n                    onSubmit: handleSubmit,\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"email\",\n                                            name: \"email\",\n                                            type: \"email\",\n                                            autoComplete: \"email\",\n                                            required: true,\n                                            className: \"input-field\",\n                                            placeholder: \"Masukkan email Anda\",\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"password\",\n                                                    name: \"password\",\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    autoComplete: \"current-password\",\n                                                    required: true,\n                                                    className: \"input-field pr-10\",\n                                                    placeholder: \"Masukkan password Anda\",\n                                                    value: password,\n                                                    onChange: (e)=>setPassword(e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"remember-me\",\n                                            name: \"remember-me\",\n                                            type: \"checkbox\",\n                                            className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"remember-me\",\n                                            className: \"ml-2 block text-sm text-gray-900\",\n                                            children: \"Ingat saya\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"font-medium text-primary-600 hover:text-primary-500\",\n                                        children: \"Lupa password?\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: loading,\n                            className: \"w-full btn-primary flex justify-center items-center\",\n                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                className: \"opacity-25\",\n                                                cx: \"12\",\n                                                cy: \"12\",\n                                                r: \"10\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                className: \"opacity-75\",\n                                                fill: \"currentColor\",\n                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Logging in...\"\n                                ]\n                            }, void 0, true) : \"Login\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500\",\n                        children: \"\\xa9 2024 DentalCare. All rights reserved.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Auth/LoginForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/AppLayout.tsx":
/*!*********************************************!*\
  !*** ./src/components/Layout/AppLayout.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppLayout: () => (/* binding */ AppLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_Auth_LoginForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Auth/LoginForm */ \"(ssr)/./src/components/Auth/LoginForm.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/Layout/Sidebar.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ AppLayout auto */ \n\n\n\n\nfunction AppLayout({ children }) {\n    const { user, profile, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    // Show loading spinner while checking auth\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n                lineNumber: 16,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this);\n    }\n    // Show login form if not authenticated\n    if (!user || !profile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Auth_LoginForm__WEBPACK_IMPORTED_MODULE_2__.LoginForm, {}, void 0, false, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n            lineNumber: 26,\n            columnNumber: 12\n        }, this);\n    }\n    // Show main app layout for authenticated users\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/AppLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/Layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _components_TenantManagement_TenantSwitcher__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/TenantManagement/TenantSwitcher */ \"(ssr)/./src/components/TenantManagement/TenantSwitcher.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Header({ title, subtitle, action }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white border-b border-gray-200 px-6 py-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, this),\n                                subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mt-1\",\n                                    children: subtitle\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        action && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-6\",\n                            children: action\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TenantManagement_TenantSwitcher__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            className: \"w-64\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Cari pasien, appointment...\",\n                                    className: \"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent w-80\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"relative p-2 text-gray-400 hover:text-gray-600 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600\",\n                            children: new Date().toLocaleDateString(\"id-ID\", {\n                                weekday: \"long\",\n                                year: \"numeric\",\n                                month: \"long\",\n                                day: \"numeric\"\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/Layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,CalendarDaysIcon,ChartBarIcon,ChevronDownIcon,ChevronUpIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,CurrencyDollarIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,CalendarDaysIcon,ChartBarIcon,ChevronDownIcon,ChevronUpIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,CurrencyDollarIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,CalendarDaysIcon,ChartBarIcon,ChevronDownIcon,ChevronUpIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,CurrencyDollarIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CalendarDaysIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,CalendarDaysIcon,ChartBarIcon,ChevronDownIcon,ChevronUpIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,CurrencyDollarIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,CalendarDaysIcon,ChartBarIcon,ChevronDownIcon,ChevronUpIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,CurrencyDollarIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,CalendarDaysIcon,ChartBarIcon,ChevronDownIcon,ChevronUpIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,CurrencyDollarIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CubeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,CalendarDaysIcon,ChartBarIcon,ChevronDownIcon,ChevronUpIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,CurrencyDollarIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,CalendarDaysIcon,ChartBarIcon,ChevronDownIcon,ChevronUpIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,CurrencyDollarIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,CalendarDaysIcon,ChartBarIcon,ChevronDownIcon,ChevronUpIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,CurrencyDollarIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,CalendarDaysIcon,ChartBarIcon,ChevronDownIcon,ChevronUpIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,CurrencyDollarIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,CalendarDaysIcon,ChartBarIcon,ChevronDownIcon,ChevronUpIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,CurrencyDollarIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,CalendarDaysIcon,ChartBarIcon,ChevronDownIcon,ChevronUpIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,CurrencyDollarIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/\",\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: \"Pasien\",\n        href: \"/patients\",\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"Jadwal\",\n        href: \"/appointments\",\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"Treatment\",\n        href: \"/treatments\",\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: \"Billing\",\n        href: \"/billing\",\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: \"Inventory\",\n        href: \"/inventory\",\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: \"Users\",\n        href: \"/users\",\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: \"Laporan\",\n        href: \"/reports\",\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        name: \"Pengaturan\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    }\n];\nfunction Sidebar() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { profile, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [showUserMenu, setShowUserMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const userMenuRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Close user menu when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {\n                setShowUserMenu(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, []);\n    const handleLogout = async ()=>{\n        if (window.confirm(\"Apakah Anda yakin ingin logout?\")) {\n            try {\n                await logout();\n            // No need to manually redirect - AuthContext will handle auth state change\n            // and AppLayout will automatically show LoginForm\n            } catch (error) {\n                console.error(\"Logout error:\", error);\n            // Optionally show error message to user\n            }\n        }\n    };\n    const handleProfileClick = ()=>{\n        setShowUserMenu(false);\n        router.push(\"/profile\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col w-64 bg-white border-r border-gray-200 h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-16 px-4 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white font-bold text-sm\",\n                                children: \"DC\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xl font-bold text-gray-900\",\n                            children: \"DentalCare\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 px-4 py-6 space-y-2\",\n                children: navigation.map((item)=>{\n                    const isActive = pathname === item.href;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: item.href,\n                        className: `sidebar-link ${isActive ? \"active\" : \"\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: \"w-5 h-5 mr-3\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 15\n                            }, this),\n                            item.name\n                        ]\n                    }, item.name, true, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                ref: userMenuRef,\n                children: [\n                    showUserMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-full left-4 right-4 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg py-2 z-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleProfileClick,\n                                className: \"w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Profil Saya\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setShowUserMenu(false);\n                                    router.push(\"/settings\");\n                                },\n                                className: \"w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-4 h-4 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Pengaturan\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                className: \"my-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleLogout,\n                                className: \"w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-4 h-4 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Logout\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowUserMenu(!showUserMenu),\n                            className: \"w-full flex items-center space-x-3 hover:bg-gray-50 rounded-lg p-2 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center\",\n                                    children: profile?.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: profile.avatar,\n                                        alt: profile.name,\n                                        className: \"w-full h-full object-cover rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-6 h-6 text-primary-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0 text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                            children: profile?.name || \"User\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 truncate\",\n                                            children: profile?.role || \"Role\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                showUserMenu ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"w-4 h-4 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_CalendarDaysIcon_ChartBarIcon_ChevronDownIcon_ChevronUpIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_CurrencyDollarIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"w-4 h-4 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/TenantManagement/TenantSwitcher.tsx":
/*!************************************************************!*\
  !*** ./src/components/TenantManagement/TenantSwitcher.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TenantSwitcher)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TenantContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TenantContext */ \"(ssr)/./src/contexts/TenantContext.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CheckIcon,ChevronDownIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BuildingOfficeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CheckIcon,ChevronDownIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CheckIcon,ChevronDownIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CheckIcon,ChevronDownIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction TenantSwitcher({ className = \"\" }) {\n    const { tenant, tenantId, loading, switchTenant, createTenant } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_2__.useTenant)();\n    const { profile } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newTenantName, setNewTenantName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // For demo purposes, we'll show available tenants\n    // In a real app, you might fetch this from a user's accessible tenants\n    const availableTenants = tenantId ? [\n        {\n            id: tenantId,\n            name: tenant?.name || \"Current Clinic\"\n        }\n    ] : [];\n    const handleSwitchTenant = async (newTenantId)=>{\n        if (newTenantId === tenantId) {\n            setIsOpen(false);\n            return;\n        }\n        try {\n            await switchTenant(newTenantId);\n            setIsOpen(false);\n        } catch (error) {\n            console.error(\"Failed to switch tenant:\", error);\n        // You might want to show a toast notification here\n        }\n    };\n    const handleCreateTenant = async ()=>{\n        if (!newTenantName.trim()) return;\n        try {\n            setIsCreating(true);\n            await createTenant({\n                name: newTenantName.trim(),\n                address: \"\",\n                phone: \"\",\n                email: profile?.email || \"\"\n            });\n            setNewTenantName(\"\");\n            setIsOpen(false);\n        } catch (error) {\n            console.error(\"Failed to create tenant:\", error);\n        // You might want to show a toast notification here\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `flex items-center space-x-2 ${className}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-gray-200 rounded-lg animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-4 bg-gray-200 rounded animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"flex items-center space-x-3 w-full px-3 py-2 text-left bg-white border border-gray-200 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-5 h-5 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-900 truncate\",\n                                children: tenant?.name || \"Select Clinic\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 truncate\",\n                                children: tenant?.address || \"No address set\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: `w-4 h-4 text-gray-400 transition-transform ${isOpen ? \"transform rotate-180\" : \"\"}`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-1\",\n                    children: [\n                        availableTenants.map((availableTenant)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleSwitchTenant(availableTenant.id),\n                                className: \"flex items-center w-full px-3 py-2 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-900\",\n                                            children: availableTenant.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 17\n                                    }, this),\n                                    availableTenant.id === tenantId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 text-primary-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, availableTenant.id, true, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 15\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-100 mt-1 pt-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-3 py-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"New clinic name\",\n                                                value: newTenantName,\n                                                onChange: (e)=>setNewTenantName(e.target.value),\n                                                className: \"flex-1 px-2 py-1 text-sm border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                                                onKeyPress: (e)=>{\n                                                    if (e.key === \"Enter\") {\n                                                        handleCreateTenant();\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleCreateTenant,\n                                                disabled: !newTenantName.trim() || isCreating,\n                                                className: \"flex items-center justify-center w-8 h-8 text-white bg-primary-600 rounded hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: isCreating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: \"Create a new clinic\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40\",\n                onClick: ()=>setIsOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/TenantManagement/TenantSwitcher.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth, async (user)=>{\n            setUser(user);\n            if (user) {\n                try {\n                    // Fetch user profile from Firestore\n                    const profileDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"users\", user.uid));\n                    if (profileDoc.exists()) {\n                        const profileData = profileDoc.data();\n                        setProfile({\n                            id: user.uid,\n                            email: user.email || \"\",\n                            ...profileData\n                        });\n                    } else {\n                        // Create default profile if doesn't exist\n                        const defaultProfile = {\n                            id: user.uid,\n                            name: user.displayName || \"User\",\n                            email: user.email || \"\",\n                            role: \"receptionist\",\n                            tenantId: `tenant_${user.uid}`,\n                            permissions: [\n                                \"read_patients\",\n                                \"manage_appointments\"\n                            ],\n                            createdAt: new Date().toISOString(),\n                            updatedAt: new Date().toISOString()\n                        };\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"users\", user.uid), defaultProfile);\n                        setProfile(defaultProfile);\n                    }\n                } catch (error) {\n                    console.error(\"Error fetching user profile:\", error);\n                }\n            } else {\n                setProfile(null);\n            }\n            setLoading(false);\n        });\n        return unsubscribe;\n    }, []);\n    const signIn = async (email, password)=>{\n        try {\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth, email, password);\n        } catch (error) {\n            throw new Error(error.message);\n        }\n    };\n    const signUp = async (email, password, profileData)=>{\n        try {\n            const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.createUserWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth, email, password);\n            const user = userCredential.user;\n            // Create user profile in Firestore\n            const newProfile = {\n                id: user.uid,\n                email: user.email || email,\n                ...profileData,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"users\", user.uid), newProfile);\n            setProfile(newProfile);\n        } catch (error) {\n            throw new Error(error.message);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signOut)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth);\n        } catch (error) {\n            throw new Error(error.message);\n        }\n    };\n    const updateProfile = async (updates)=>{\n        if (!user || !profile) throw new Error(\"No user logged in\");\n        try {\n            const updatedProfile = {\n                ...profile,\n                ...updates,\n                updatedAt: new Date().toISOString()\n            };\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"users\", user.uid), updatedProfile, {\n                merge: true\n            });\n            setProfile(updatedProfile);\n        } catch (error) {\n            throw new Error(error.message);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            profile,\n            loading,\n            signIn,\n            signUp,\n            logout,\n            updateProfile\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 146,\n        columnNumber: 5\n    }, this);\n}\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within AuthProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/TenantContext.tsx":
/*!****************************************!*\
  !*** ./src/contexts/TenantContext.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TenantProvider: () => (/* binding */ TenantProvider),\n/* harmony export */   useTenant: () => (/* binding */ useTenant)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ TenantProvider,useTenant auto */ \n\n\n\n\nconst TenantContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction TenantProvider({ children }) {\n    const [tenant, setTenant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tenantId, setTenantId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { user, profile } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadTenant = async ()=>{\n            if (!user || !profile?.tenantId) {\n                setTenant(null);\n                setTenantId(null);\n                setLoading(false);\n                return;\n            }\n            try {\n                const tenantDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"dentalcare\", profile.tenantId, \"settings\", \"clinic\"));\n                if (tenantDoc.exists()) {\n                    const tenantData = tenantDoc.data();\n                    setTenant({\n                        ...tenantData,\n                        id: profile.tenantId\n                    });\n                    setTenantId(profile.tenantId);\n                } else {\n                    // Create default tenant settings if they don't exist\n                    const defaultTenant = {\n                        id: profile.tenantId,\n                        name: \"Klinik Gigi\",\n                        address: \"\",\n                        phone: \"\",\n                        email: profile.email,\n                        settings: {\n                            timezone: \"Asia/Jakarta\",\n                            currency: \"IDR\",\n                            dateFormat: \"DD/MM/YYYY\",\n                            businessHours: {\n                                start: \"08:00\",\n                                end: \"17:00\",\n                                days: [\n                                    \"monday\",\n                                    \"tuesday\",\n                                    \"wednesday\",\n                                    \"thursday\",\n                                    \"friday\",\n                                    \"saturday\"\n                                ]\n                            }\n                        },\n                        createdAt: new Date().toISOString(),\n                        updatedAt: new Date().toISOString()\n                    };\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"dentalcare\", profile.tenantId, \"settings\", \"clinic\"), defaultTenant);\n                    setTenant(defaultTenant);\n                    setTenantId(profile.tenantId);\n                }\n            } catch (error) {\n                console.error(\"Error loading tenant:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        loadTenant();\n    }, [\n        user,\n        profile\n    ]);\n    const switchTenant = async (newTenantId)=>{\n        if (!user) throw new Error(\"No user logged in\");\n        try {\n            setLoading(true);\n            // Update user's tenantId in global users collection\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"users\", user.uid), {\n                tenantId: newTenantId,\n                updatedAt: new Date().toISOString()\n            }, {\n                merge: true\n            });\n            // Load new tenant data\n            const tenantDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"dentalcare\", newTenantId, \"settings\", \"clinic\"));\n            if (tenantDoc.exists()) {\n                const tenantData = tenantDoc.data();\n                setTenant({\n                    ...tenantData,\n                    id: newTenantId\n                });\n                setTenantId(newTenantId);\n            }\n        } catch (error) {\n            console.error(\"Error switching tenant:\", error);\n            throw new Error(\"Failed to switch tenant\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const updateTenant = async (updates)=>{\n        if (!tenantId || !tenant) throw new Error(\"No tenant selected\");\n        try {\n            const updatedTenant = {\n                ...tenant,\n                ...updates,\n                updatedAt: new Date().toISOString()\n            };\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"dentalcare\", tenantId, \"settings\", \"clinic\"), updatedTenant, {\n                merge: true\n            });\n            setTenant(updatedTenant);\n        } catch (error) {\n            console.error(\"Error updating tenant:\", error);\n            throw new Error(\"Failed to update tenant\");\n        }\n    };\n    const createTenant = async (tenantData)=>{\n        if (!user) throw new Error(\"No user logged in\");\n        try {\n            // Generate tenant ID (could be UUID or custom format)\n            const newTenantId = `tenant_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n            const newTenant = {\n                ...tenantData,\n                id: newTenantId,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            // Create tenant settings\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"dentalcare\", newTenantId, \"settings\", \"clinic\"), newTenant);\n            // Update user's tenantId\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"users\", user.uid), {\n                tenantId: newTenantId,\n                updatedAt: new Date().toISOString()\n            }, {\n                merge: true\n            });\n            setTenant(newTenant);\n            setTenantId(newTenantId);\n            return newTenantId;\n        } catch (error) {\n            console.error(\"Error creating tenant:\", error);\n            throw new Error(\"Failed to create tenant\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TenantContext.Provider, {\n        value: {\n            tenant,\n            tenantId,\n            loading,\n            switchTenant,\n            updateTenant,\n            createTenant\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\contexts\\\\TenantContext.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, this);\n}\nconst useTenant = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TenantContext);\n    if (!context) {\n        throw new Error(\"useTenant must be used within TenantProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/TenantContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(ssr)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyDv9u4NhGvNFVVsou_IrXMO__rlfXfCKIk\",\n    authDomain: \"widigital-d6110.firebaseapp.com\",\n    projectId: \"widigital-d6110\",\n    storageBucket: \"widigital-d6110.firebasestorage.app\",\n    messagingSenderId: \"329879577024\",\n    appId: \"1:329879577024:web:0d8752f8175569f67d6825\"\n};\n// Initialize Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n// Initialize Firebase services\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)(app);\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.getAuth)(app);\n// Connect to emulators in development (disabled for now)\n// if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {\n//   try {\n//     // Only connect if not already connected\n//     if (!auth.config.emulator) {\n//       connectAuthEmulator(auth, 'http://localhost:9099');\n//     }\n//     // @ts-ignore\n//     if (!db._delegate._databaseId.projectId.includes('localhost')) {\n//       connectFirestoreEmulator(db, 'localhost', 8080);\n//     }\n//   } catch (error) {\n//     console.log('Emulators already connected or not available');\n//   }\n// }\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/queryClient.ts":
/*!********************************!*\
  !*** ./src/lib/queryClient.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryClient: () => (/* binding */ queryClient)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_0__.QueryClient({\n    defaultOptions: {\n        queries: {\n            staleTime: 5 * 60 * 1000,\n            gcTime: 10 * 60 * 1000,\n            retry: (failureCount, error)=>{\n                // Don't retry on auth errors\n                if (error?.code === \"permission-denied\" || error?.code === \"unauthenticated\") {\n                    return false;\n                }\n                return failureCount < 3;\n            },\n            refetchOnWindowFocus: false\n        },\n        mutations: {\n            retry: false\n        }\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3F1ZXJ5Q2xpZW50LnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9EO0FBRTdDLE1BQU1DLGNBQWMsSUFBSUQsOERBQVdBLENBQUM7SUFDekNFLGdCQUFnQjtRQUNkQyxTQUFTO1lBQ1BDLFdBQVcsSUFBSSxLQUFLO1lBQ3BCQyxRQUFRLEtBQUssS0FBSztZQUNsQkMsT0FBTyxDQUFDQyxjQUFjQztnQkFDcEIsNkJBQTZCO2dCQUM3QixJQUFJQSxPQUFPQyxTQUFTLHVCQUF1QkQsT0FBT0MsU0FBUyxtQkFBbUI7b0JBQzVFLE9BQU87Z0JBQ1Q7Z0JBQ0EsT0FBT0YsZUFBZTtZQUN4QjtZQUNBRyxzQkFBc0I7UUFDeEI7UUFDQUMsV0FBVztZQUNUTCxPQUFPO1FBQ1Q7SUFDRjtBQUNGLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZW50YWwtY2xpbmljLXVpLy4vc3JjL2xpYi9xdWVyeUNsaWVudC50cz81MWIwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFF1ZXJ5Q2xpZW50IH0gZnJvbSAnQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5JztcblxuZXhwb3J0IGNvbnN0IHF1ZXJ5Q2xpZW50ID0gbmV3IFF1ZXJ5Q2xpZW50KHtcbiAgZGVmYXVsdE9wdGlvbnM6IHtcbiAgICBxdWVyaWVzOiB7XG4gICAgICBzdGFsZVRpbWU6IDUgKiA2MCAqIDEwMDAsIC8vIDUgbWludXRlc1xuICAgICAgZ2NUaW1lOiAxMCAqIDYwICogMTAwMCwgLy8gMTAgbWludXRlcyAod2FzIGNhY2hlVGltZSlcbiAgICAgIHJldHJ5OiAoZmFpbHVyZUNvdW50LCBlcnJvcjogYW55KSA9PiB7XG4gICAgICAgIC8vIERvbid0IHJldHJ5IG9uIGF1dGggZXJyb3JzXG4gICAgICAgIGlmIChlcnJvcj8uY29kZSA9PT0gJ3Blcm1pc3Npb24tZGVuaWVkJyB8fCBlcnJvcj8uY29kZSA9PT0gJ3VuYXV0aGVudGljYXRlZCcpIHtcbiAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGZhaWx1cmVDb3VudCA8IDM7XG4gICAgICB9LFxuICAgICAgcmVmZXRjaE9uV2luZG93Rm9jdXM6IGZhbHNlLFxuICAgIH0sXG4gICAgbXV0YXRpb25zOiB7XG4gICAgICByZXRyeTogZmFsc2UsXG4gICAgfSxcbiAgfSxcbn0pO1xuIl0sIm5hbWVzIjpbIlF1ZXJ5Q2xpZW50IiwicXVlcnlDbGllbnQiLCJkZWZhdWx0T3B0aW9ucyIsInF1ZXJpZXMiLCJzdGFsZVRpbWUiLCJnY1RpbWUiLCJyZXRyeSIsImZhaWx1cmVDb3VudCIsImVycm9yIiwiY29kZSIsInJlZmV0Y2hPbldpbmRvd0ZvY3VzIiwibXV0YXRpb25zIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/queryClient.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7eee4f443319\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVudGFsLWNsaW5pYy11aS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/YWM2MyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjdlZWU0ZjQ0MzMxOVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n\n\n\nconst metadata = {\n    title: \"DentalCare - Sistem Manajemen Klinik Gigi\",\n    description: \"Aplikasi manajemen klinik gigi yang terintegrasi\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"id\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF1QjtBQUVpQjtBQUVqQyxNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztzQkFDQyw0RUFBQ1IsaURBQVNBOzBCQUNQSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVudGFsLWNsaW5pYy11aS8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAnLi9nbG9iYWxzLmNzcyc7XG5pbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCc7XG5pbXBvcnQgeyBQcm92aWRlcnMgfSBmcm9tICcuL3Byb3ZpZGVycyc7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnRGVudGFsQ2FyZSAtIFNpc3RlbSBNYW5hamVtZW4gS2xpbmlrIEdpZ2knLFxuICBkZXNjcmlwdGlvbjogJ0FwbGlrYXNpIG1hbmFqZW1lbiBrbGluaWsgZ2lnaSB5YW5nIHRlcmludGVncmFzaScsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImlkXCI+XG4gICAgICA8Ym9keT5cbiAgICAgICAgPFByb3ZpZGVycz5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvUHJvdmlkZXJzPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJQcm92aWRlcnMiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/profile/page.tsx":
/*!**********************************!*\
  !*** ./src/app/profile/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\dentalcare.id\src\app\profile\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\dentalcare.id\src\app\providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\dentalcare.id\src\app\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@firebase","vendor-chunks/@grpc","vendor-chunks/firebase","vendor-chunks/protobufjs","vendor-chunks/@tanstack","vendor-chunks/long","vendor-chunks/@protobufjs","vendor-chunks/@heroicons","vendor-chunks/lodash.camelcase","vendor-chunks/idb"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=D%3A%5Cdentalcare.id%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cdentalcare.id&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();